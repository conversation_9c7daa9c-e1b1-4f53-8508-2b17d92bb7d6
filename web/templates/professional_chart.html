<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业股票技术分析图表</title>
    <script src="https://cdn.jsdelivr.net/npm/lightweight-charts@4.1.1/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #131722;
            color: #d1d4dc;
            overflow: hidden;
        }

        .header {
            background: #1e222d;
            padding: 12px 20px;
            border-bottom: 1px solid #2a2e39;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .stock-info h1 {
            margin: 0;
            font-size: 20px;
            color: #ffffff;
            font-weight: 600;
        }

        .stock-price {
            font-size: 18px;
            font-weight: bold;
            color: #26a69a;
            margin-right: 15px;
        }

        .refresh-btn {
            background: #2962ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        .refresh-btn:hover {
            background: #1e53e5;
        }

        .chart-container {
            height: calc(100vh - 60px);
            display: flex;
            flex-direction: column;
        }

        .main-chart {
            height: 40%;
            position: relative;
            border-bottom: 1px solid #2a2e39;
        }

        .sub-charts {
            height: 60%;
            display: flex;
            flex-direction: column;
        }

        .sub-chart {
            height: 16.67%; /* 6个子图表，每个占1/6 */
            position: relative;
            border-bottom: 1px solid #2a2e39;
        }

        .sub-chart:last-child {
            border-bottom: none;
        }

        .chart-title {
            position: absolute;
            top: 8px;
            left: 12px;
            font-size: 12px;
            color: #787b86;
            z-index: 10;
            pointer-events: none;
            font-weight: 500;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #787b86;
            font-size: 14px;
        }

        .error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ef5350;
            font-size: 14px;
            text-align: center;
        }

        .status-info {
            position: absolute;
            top: 8px;
            right: 12px;
            font-size: 11px;
            color: #787b86;
            z-index: 10;
        }

        .main-chart {
            height: 35%;
            border-bottom: 1px solid #2a2e39;
        }

        .sub-charts {
            height: 65%;
            display: flex;
            flex-direction: column;
        }

        .sub-chart {
            flex: 1;
            border-bottom: 1px solid #2a2e39;
            position: relative;
        }

        .chart-title {
            position: absolute;
            top: 10px;
            left: 15px;
            font-size: 14px;
            font-weight: bold;
            color: #787b86;
            z-index: 10;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #787b86;
            font-size: 16px;
        }

        .error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ef5350;
            text-align: center;
        }

        .btn {
            background: #2962ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }

        .btn:hover {
            background: #1e4ba8;
        }


    </style>
</head>
<body>
    <div class="header">
        <div class="stock-info">
            <h1 id="stockTitle">加载中...</h1>
        </div>
        <div class="controls">
            <span class="stock-price" id="stockPrice">--</span>
            <select id="data-limit-select" class="form-select form-select-sm me-2" style="width: 120px; display: inline-block;">
                <option value="100">100天</option>
                <option value="250">250天</option>
                <option value="500">500天</option>
                <option value="1000" selected>1000天</option>
                <option value="2000">2000天</option>
            </select>
            <button class="refresh-btn" id="refreshBtn" onclick="refreshData()">
                刷新
            </button>
        </div>
    </div>

    <div class="chart-container">
        <div class="main-chart" id="mainChart">
            <div class="chart-title">K线图 & 移动平均线</div>
            <div class="status-info" id="mainStatus"></div>
            <div class="loading" id="mainLoading">正在加载数据...</div>
        </div>

        <div class="sub-charts">
            <div class="sub-chart" id="mcsiMacdChart">
                <div class="chart-title">MCSI MACD</div>
                <div class="status-info" id="mcsiMacdStatus"></div>
            </div>

            <div class="sub-chart" id="mcsiMmtChart">
                <div class="chart-title">MCSI MMT</div>
                <div class="status-info" id="mcsiMmtStatus"></div>
            </div>

            <div class="sub-chart" id="mcsiRsiChart">
                <div class="chart-title">MCSI RSI</div>
                <div class="status-info" id="mcsiRsiStatus"></div>
            </div>

            <div class="sub-chart" id="mcsiTtmChart">
                <div class="chart-title">MCSI TTM</div>
                <div class="status-info" id="mcsiTtmStatus"></div>
            </div>
        </div>
    </div>
    
    <script>
        let mainChart = null;
        let mcsiMacdChart = null;
        let mcsiMmtChart = null;
        let mcsiRsiChart = null;
        let mcsiTtmChart = null;
        let currentStock = null;

        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 日志函数
        function log(message) {
            console.log('[Chart]', message);
        }

        // 显示错误
        function showError(message, containerId = 'mainChart') {
            const container = document.getElementById(containerId);
            const loading = container.querySelector('.loading');
            if (loading) loading.style.display = 'none';

            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            container.appendChild(errorDiv);
        }

        // 更新状态信息
        function updateStatus(containerId, message) {
            const statusElement = document.getElementById(containerId);
            if (statusElement) {
                statusElement.textContent = message;
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            const stockCode = getUrlParameter('stock') || '300584';
            const stockName = getUrlParameter('name') || '海辰药业';
            const dataLimit = getUrlParameter('limit') || '1000';

            currentStock = { code: stockCode, name: stockName };

            // 设置数据量选择器的初始值
            const dataLimitSelect = document.getElementById('data-limit-select');
            if (dataLimitSelect) {
                dataLimitSelect.value = dataLimit;
            }

            log('页面加载完成');
            log('股票代码: ' + stockCode);
            log('股票名称: ' + stockName);
            log('数据量: ' + dataLimit);

            // 检查LightweightCharts
            if (typeof LightweightCharts === 'undefined') {
                showError('LightweightCharts库未加载');
                return;
            }

            log('LightweightCharts库正常');

            // 更新标题
            document.getElementById('stockTitle').textContent = `${stockName} (${stockCode})`;

            // 初始化图表
            initializeCharts();

            // 加载数据
            loadStockData(stockCode);
        });

        // 刷新数据
        function refreshData() {
            if (currentStock) {
                log('刷新数据...');
                loadStockData(currentStock.code);
            }
        }
        
        // 初始化图表
        function initializeCharts() {
            log('初始化图表...');

            // 重置时间轴同步标志
            timeAxisSyncSetup = false;

            try {
                // 主图表 - K线和移动平均线
                mainChart = LightweightCharts.createChart(document.getElementById('mainChart'), {
                    width: document.getElementById('mainChart').clientWidth,
                    height: document.getElementById('mainChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        fixLeftEdge: true,
                        fixRightEdge: false, // 允许右侧扩展
                        rightOffset: 50, // 增加右侧留白空间
                        barSpacing: 6, // 设置K线间距
                        minBarSpacing: 0.5, // 最小K线间距
                        visible: false, // 隐藏主图表的时间轴
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                });

                log('主图表初始化完成');

            } catch (error) {
                log('图表初始化失败: ' + error.message);
                showError('图表初始化失败: ' + error.message);
            }
        }

        // 加载股票数据
        async function loadStockData(stockCode) {
            try {
                log('开始加载股票数据: ' + stockCode);
                updateStatus('mainStatus', '加载中...');

                // 获取数据量参数
                const dataLimit = document.getElementById('data-limit-select').value;
                log('数据量参数: ' + dataLimit);

                const apiUrl = `/api/stock_chart/${stockCode}?limit=${dataLimit}`;
                log('API URL: ' + apiUrl);

                const response = await fetch(apiUrl);
                log('API响应状态: ' + response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                log('API响应数据: ' + JSON.stringify(data).substring(0, 200) + '...');

                if (data.success) {
                    log('数据加载成功，数据点数: ' + data.data_points);
                    log('日期数组长度: ' + data.dates.length);
                    log('价格数据: ' + JSON.stringify(data.prices).substring(0, 100) + '...');

                    renderCharts(data);

                    // 更新价格显示
                    if (data.prices && data.prices.close && data.prices.close.length > 0) {
                        const latestPrice = data.prices.close[data.prices.close.length - 1];
                        document.getElementById('stockPrice').textContent = latestPrice.toFixed(2);
                        log('最新价格: ' + latestPrice);
                    }
                } else {
                    const errorMsg = data.message || data.error || '未知错误';
                    log('数据加载失败: ' + errorMsg);
                    showError('数据加载失败: ' + errorMsg);
                }
            } catch (error) {
                log('加载失败: ' + error.message);
                console.error('详细错误:', error);
                showError('网络错误: ' + error.message);
            }
        }

        // 渲染图表
        function renderCharts(data) {
            try {
                log('开始渲染图表...');
                log('数据验证: dates=' + (data.dates ? data.dates.length : 'null') +
                    ', prices=' + (data.prices ? 'ok' : 'null') +
                    ', ma=' + (data.ma ? 'ok' : 'null') +
                    ', macd=' + (data.macd ? 'ok' : 'null') +
                    ', rsi=' + (data.rsi ? data.rsi.length : 'null') +
                    ', mcsi_indicators=' + (data.mcsi_indicators ? 'ok' : 'null'));

                // 清理现有图表（确保完全重新初始化）
                if (mainChart) {
                    log('清理现有主图表');
                    mainChart.remove();
                    mainChart = null;
                }

                if (mcsiMacdChart) {
                    log('清理现有MCSI MACD图表');
                    mcsiMacdChart.remove();
                    mcsiMacdChart = null;
                }
                if (mcsiMmtChart) {
                    log('清理现有MCSI MMT图表');
                    mcsiMmtChart.remove();
                    mcsiMmtChart = null;
                }
                if (mcsiRsiChart) {
                    log('清理现有MCSI RSI图表');
                    mcsiRsiChart.remove();
                    mcsiRsiChart = null;
                }
                if (mcsiTtmChart) {
                    log('清理现有MCSI TD9图表');
                    mcsiTtmChart.remove();
                    mcsiTtmChart = null;
                }

                // 重新初始化图表
                log('重新初始化图表...');
                initializeCharts();

                // 渲染主图表
                log('渲染主图表...');
                renderMainChart(data);

                // 渲染MCSI指标图表
                if (data.mcsi_indicators) {
                    log('渲染MCSI MACD图表...');
                    renderMCSIMACDChart(data);

                    log('渲染MCSI MMT图表...');
                    renderMCSIMMTChart(data);

                    log('渲染MCSI RSI图表...');
                    renderMCSIRSIChart(data);

                    log('渲染MCSI TD9图表...');
                    renderMCSITTMChart(data);
                }

                log('图表渲染完成');

                // 重新设置时间轴同步
                setTimeout(() => {
                    log('设置MCSI指标时间轴同步...');
                    setupTimeAxisSync();
                    performInitialSync();
                }, 100);

            } catch (error) {
                log('图表渲染失败: ' + error.message);
                console.error('图表渲染详细错误:', error);
                showError('图表渲染失败: ' + error.message);
            }
        }

        // 渲染主图表（K线 + 移动平均线）
        function renderMainChart(data) {
            if (!mainChart || !data.dates || !data.prices) {
                log('主图表数据不完整');
                return;
            }

            // 隐藏加载提示
            const loading = document.getElementById('mainLoading');
            if (loading) loading.style.display = 'none';

            // K线数据
            const candleData = data.dates.map((date, i) => ({
                time: date,
                open: data.prices.open[i],
                high: data.prices.high[i],
                low: data.prices.low[i],
                close: data.prices.close[i]
            }));

            const candleSeries = mainChart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
            });
            candleSeries.setData(candleData);
            log('K线数据设置完成: ' + candleData.length + ' 个数据点');

            // 移动平均线
            if (data.ma) {
                // MA5 - 蓝色
                if (data.ma.ma5) {
                    const ma5Data = data.dates.map((date, i) => ({
                        time: date,
                        value: data.ma.ma5[i]
                    })).filter(item => item.value !== null && item.value !== undefined && item.value > 0);

                    if (ma5Data.length > 0) {
                        const ma5Series = mainChart.addLineSeries({
                            color: '#2196f3',
                            lineWidth: 2,
                            title: 'MA5'
                        });
                        ma5Series.setData(ma5Data);
                        log('MA5数据点: ' + ma5Data.length);
                    }
                }

                // MA20 - 橙色
                if (data.ma.ma20) {
                    const ma20Data = data.dates.map((date, i) => ({
                        time: date,
                        value: data.ma.ma20[i]
                    })).filter(item => item.value !== null && item.value !== undefined && item.value > 0);

                    if (ma20Data.length > 0) {
                        const ma20Series = mainChart.addLineSeries({
                            color: '#ff9800',
                            lineWidth: 2,
                            title: 'MA20'
                        });
                        ma20Series.setData(ma20Data);
                        log('MA20数据点: ' + ma20Data.length);
                    }
                }

                // MA50 - 粉色
                if (data.ma.ma50) {
                    const ma50Data = data.dates.map((date, i) => ({
                        time: date,
                        value: data.ma.ma50[i]
                    })).filter(item => item.value !== null && item.value !== undefined && item.value > 0);

                    if (ma50Data.length > 0) {
                        const ma50Series = mainChart.addLineSeries({
                            color: '#e91e63',
                            lineWidth: 2,
                            title: 'MA50'
                        });
                        ma50Series.setData(ma50Data);
                        log('MA50数据点: ' + ma50Data.length);
                    }
                }

                // MA200 - 紫色
                if (data.ma.ma200) {
                    const ma200Data = data.dates.map((date, i) => ({
                        time: date,
                        value: data.ma.ma200[i]
                    })).filter(item => item.value !== null && item.value !== undefined && item.value > 0);

                    if (ma200Data.length > 0) {
                        const ma200Series = mainChart.addLineSeries({
                            color: '#9c27b0',
                            lineWidth: 2,
                            title: 'MA200'
                        });
                        ma200Series.setData(ma200Data);
                        log('MA200数据点: ' + ma200Data.length);
                    }
                }
            }

            updateStatus('mainStatus', `K线: ${candleData.length}点`);
        }





        // 时间轴同步状态标志
        let timeAxisSyncSetup = false;

        // 设置时间轴同步 - 仅针对MCSI指标
        function setupTimeAxisSync() {
            if (!mainChart || !mcsiMacdChart || !mcsiMmtChart || !mcsiRsiChart || !mcsiTtmChart) {
                log('图表未完全初始化，跳过时间轴同步');
                return;
            }

            // 避免重复设置
            if (timeAxisSyncSetup) {
                log('时间轴同步已设置，跳过重复设置');
                return;
            }

            log('设置MCSI指标时间轴同步...');
            timeAxisSyncSetup = true;

            // 简化的同步函数
            function syncAllCharts(masterChart) {
                const masterLogicalRange = masterChart.timeScale().getVisibleLogicalRange();

                if (masterLogicalRange) {
                    const charts = [mainChart, mcsiMacdChart, mcsiMmtChart, mcsiRsiChart, mcsiTtmChart];
                    charts.forEach(chart => {
                        if (chart && chart !== masterChart) {
                            chart.timeScale().setVisibleLogicalRange(masterLogicalRange);
                        }
                    });
                }
            }

            // 设置简化的时间轴同步
            const charts = [mainChart, mcsiMacdChart, mcsiMmtChart, mcsiRsiChart, mcsiTtmChart];

            charts.forEach(chart => {
                if (chart) {
                    chart.timeScale().subscribeVisibleLogicalRangeChange(() => {
                        syncAllCharts(chart);
                    });
                }
            });

            log('MCSI指标时间轴同步设置完成');
        }

        // 执行初始同步
        function performInitialSync() {
            if (!mainChart) {
                log('主图表未初始化，跳过初始同步');
                return;
            }

            try {
                log('执行MCSI指标初始同步');

                // 让所有图表自适应内容
                const charts = [mainChart, mcsiMacdChart, mcsiMmtChart, mcsiRsiChart, mcsiTtmChart];
                charts.forEach(chart => {
                    if (chart) {
                        chart.timeScale().fitContent();
                    }
                });

                log('初始同步完成');
            } catch (error) {
                log('初始同步失败: ' + error.message);
            }
        }

        // 🔒 安全的MCSI MACD图表 - 仅显示评分线
        function renderMCSIMACDChart(data) {
            if (!data.mcsi_indicators || !data.mcsi_indicators.mcsi_macd) {
                log('MCSI MACD数据不存在');
                updateStatus('mcsiMacdStatus', '无数据');
                return;
            }

            try {
                const mcsiMacdData = data.mcsi_indicators.mcsi_macd;

                // 检查是否有可用的.so文件数据
                if (!mcsiMacdData.available) {
                    log('MCSI MACD高级单元不可用');
                    updateStatus('mcsiMacdStatus', '⚠️ 仅限.so版本');
                    return;
                }

                // 初始化MCSI MACD图表
                mcsiMacdChart = LightweightCharts.createChart(document.getElementById('mcsiMacdChart'), {
                    width: document.getElementById('mcsiMacdChart').clientWidth,
                    height: document.getElementById('mcsiMacdChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        visible: false,
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                });

                // 🔒 仅显示MACD评分线 - 不暴露算法细节
                if (mcsiMacdData.macd_score) {
                    const scoreData = data.dates.map((date, i) => ({
                        time: date,
                        value: mcsiMacdData.macd_score[i]
                    })).filter(item => item.value !== null && item.value !== undefined && !isNaN(item.value));

                    const scoreSeries = mcsiMacdChart.addLineSeries({
                        color: '#00ff00', // 绿色评分线
                        lineWidth: 3,
                        title: 'MCSI MACD Score'
                    });
                    scoreSeries.setData(scoreData);
                }

                // 添加零轴线作为参考
                const zeroLineData = data.dates.map(date => ({
                    time: date,
                    value: 0
                }));
                const zeroLineSeries = mcsiMacdChart.addLineSeries({
                    color: '#808080',
                    lineWidth: 1,
                    lineStyle: 2, // 虚线
                    title: 'Zero Line'
                });
                zeroLineSeries.setData(zeroLineData);

                updateStatus('mcsiMacdStatus', '🔒 MCSI MACD (安全模式)');

            } catch (error) {
                log('MCSI MACD图表渲染失败: ' + error.message);
                updateStatus('mcsiMacdStatus', '渲染失败');
            }
        }

        // 辅助函数：获取MACD柱状图颜色
        function getMacdHistogramColor(colorName) {
            const colorMap = {
                'aqua': '#00ffff',
                'blue': '#0000ff',
                'red': '#ff0000',
                'maroon': '#800000',
                'gray': '#808080'
            };
            return colorMap[colorName] || '#808080';
        }

        // 🔒 安全的MCSI MMT图表 - 仅显示评分线
        function renderMCSIMMTChart(data) {
            if (!data.mcsi_indicators || !data.mcsi_indicators.mcsi_mmt) {
                log('MCSI MMT数据不存在');
                updateStatus('mcsiMmtStatus', '无数据');
                return;
            }

            try {
                const mcsiMmtData = data.mcsi_indicators.mcsi_mmt;

                // 检查是否有可用的.so文件数据
                if (!mcsiMmtData.available) {
                    log('MCSI MMT高级单元不可用');
                    updateStatus('mcsiMmtStatus', '⚠️ 仅限.so版本');
                    return;
                }

                // 初始化MCSI MMT图表
                mcsiMmtChart = LightweightCharts.createChart(document.getElementById('mcsiMmtChart'), {
                    width: document.getElementById('mcsiMmtChart').clientWidth,
                    height: document.getElementById('mcsiMmtChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        visible: false,
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                });

                // 🔒 仅显示MMT评分线 - 不暴露CSI缓冲区、轨道、背离等算法细节
                if (mcsiMmtData.mmt_score) {
                    const mmtScoreData = data.dates.map((date, i) => ({
                        time: date,
                        value: mcsiMmtData.mmt_score[i]
                    })).filter(item => item.value !== null && item.value !== undefined && !isNaN(item.value));

                    const mmtScoreSeries = mcsiMmtChart.addLineSeries({
                        color: '#ffff00', // 黄色评分线
                        lineWidth: 3,
                        title: 'MCSI MMT Score'
                    });
                    mmtScoreSeries.setData(mmtScoreData);
                }

                // 添加零轴线作为参考
                const zeroLineData = data.dates.map(date => ({
                    time: date,
                    value: 0
                }));
                const zeroLineSeries = mcsiMmtChart.addLineSeries({
                    color: '#808080',
                    lineWidth: 1,
                    lineStyle: 2, // 虚线
                    title: 'Zero Line'
                });
                zeroLineSeries.setData(zeroLineData);

                updateStatus('mcsiMmtStatus', '🔒 MCSI MMT (安全模式)');

            } catch (error) {
                log('MCSI MMT图表渲染失败: ' + error.message);
                updateStatus('mcsiMmtStatus', '渲染失败');
            }
        }

        // 🔒 安全的MCSI RSI图表 - 仅显示评分线
        function renderMCSIRSIChart(data) {
            if (!data.mcsi_indicators || !data.mcsi_indicators.mcsi_rsi) {
                log('MCSI RSI数据不存在');
                updateStatus('mcsiRsiStatus', '无数据');
                return;
            }

            try {
                const mcsiRsiData = data.mcsi_indicators.mcsi_rsi;

                // 检查是否有可用的.so文件数据
                if (!mcsiRsiData.available) {
                    log('MCSI RSI高级单元不可用');
                    updateStatus('mcsiRsiStatus', '⚠️ 仅限.so版本');
                    return;
                }

                // 初始化MCSI RSI图表
                mcsiRsiChart = LightweightCharts.createChart(document.getElementById('mcsiRsiChart'), {
                    width: document.getElementById('mcsiRsiChart').clientWidth,
                    height: document.getElementById('mcsiRsiChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        visible: false,
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                });

                // 🔒 仅显示RSI评分线 - 不暴露日周线CRSI、上下轨等算法细节
                if (mcsiRsiData.rsi_score) {
                    const rsiScoreData = data.dates.map((date, i) => ({
                        time: date,
                        value: mcsiRsiData.rsi_score[i]
                    })).filter(item => item.value !== null && item.value !== undefined && !isNaN(item.value));

                    const rsiScoreSeries = mcsiRsiChart.addLineSeries({
                        color: '#ff6600', // 橙色评分线
                        lineWidth: 3,
                        title: 'MCSI RSI Score'
                    });
                    rsiScoreSeries.setData(rsiScoreData);
                }

                // 添加零轴线作为参考
                const zeroLineData = data.dates.map(date => ({
                    time: date,
                    value: 0
                }));
                const zeroLineSeries = mcsiRsiChart.addLineSeries({
                    color: '#808080',
                    lineWidth: 1,
                    lineStyle: 2, // 虚线
                    title: 'Zero Line'
                });
                zeroLineSeries.setData(zeroLineData);

                updateStatus('mcsiRsiStatus', '🔒 MCSI RSI (安全模式)');

            } catch (error) {
                log('MCSI RSI图表渲染失败: ' + error.message);
                updateStatus('mcsiRsiStatus', '渲染失败');
            }
        }

        // 🔒 安全的MCSI TTM图表 - 仅显示评分线
        function renderMCSITTMChart(data) {
            if (!data.mcsi_indicators || !data.mcsi_indicators.mcsi_ttm) {
                log('MCSI TTM数据不存在');
                updateStatus('mcsiTtmStatus', '无数据');
                return;
            }

            try {
                const mcsiTtmData = data.mcsi_indicators.mcsi_ttm;

                // 检查是否有可用的.so文件数据
                if (!mcsiTtmData.available) {
                    log('MCSI TTM高级单元不可用');
                    updateStatus('mcsiTtmStatus', '⚠️ 仅限.so版本');
                    return;
                }

                // 初始化MCSI TTM图表
                mcsiTtmChart = LightweightCharts.createChart(document.getElementById('mcsiTtmChart'), {
                    width: document.getElementById('mcsiTtmChart').clientWidth,
                    height: document.getElementById('mcsiTtmChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        visible: true, // TTM图表显示时间轴
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                });

                // 🔒 仅显示TTM评分线 - 不暴露计数器、形态识别等算法细节
                if (mcsiTtmData.ttm_score) {
                    const ttmScoreData = data.dates.map((date, i) => ({
                        time: date,
                        value: mcsiTtmData.ttm_score[i]
                    })).filter(item => item.value !== null && item.value !== undefined && !isNaN(item.value));

                    const ttmScoreSeries = mcsiTtmChart.addLineSeries({
                        color: '#9966ff', // 紫色评分线
                        lineWidth: 3,
                        title: 'MCSI TTM Score'
                    });
                    ttmScoreSeries.setData(ttmScoreData);
                }

                // 添加零轴线作为参考
                const zeroLineData = data.dates.map(date => ({
                    time: date,
                    value: 0
                }));
                const zeroLineSeries = mcsiTtmChart.addLineSeries({
                    color: '#808080',
                    lineWidth: 1,
                    lineStyle: 2, // 虚线
                    title: 'Zero Line'
                });
                zeroLineSeries.setData(zeroLineData);

                updateStatus('mcsiTtmStatus', '🔒 MCSI TTM (安全模式)');

            } catch (error) {
                log('MCSI TTM图表渲染失败: ' + error.message);
                updateStatus('mcsiTtmStatus', '渲染失败');
            }
        }

        // 强制调整所有MCSI图表以显示完整内容
        function fitChartsToContent() {
            if (!mainChart) {
                log('主图表未初始化，跳过内容适配');
                return;
            }

            try {
                log('开始调整MCSI图表内容适配...');

                // 主图表适配内容
                mainChart.timeScale().fitContent();
                log('主图表内容适配完成');

                // 延迟同步MCSI图表，确保主图表适配完成
                setTimeout(() => {
                    const mainLogicalRange = mainChart.timeScale().getVisibleLogicalRange();
                    if (mainLogicalRange) {
                        if (mcsiMacdChart) mcsiMacdChart.timeScale().setVisibleLogicalRange(mainLogicalRange);
                        if (mcsiMmtChart) mcsiMmtChart.timeScale().setVisibleLogicalRange(mainLogicalRange);
                        if (mcsiRsiChart) mcsiRsiChart.timeScale().setVisibleLogicalRange(mainLogicalRange);
                        if (mcsiTtmChart) mcsiTtmChart.timeScale().setVisibleLogicalRange(mainLogicalRange);
                        log('所有MCSI图表内容适配完成');
                    }
                }, 50);

            } catch (error) {
                log('MCSI图表内容适配失败: ' + error.message);
            }
        }

        // 数据量选择器事件
        document.getElementById('data-limit-select').addEventListener('change', function() {
            const newDataLimit = this.value;
            log('数据量已更改为: ' + newDataLimit + '天');

            if (currentStock) {
                // 显示加载状态
                updateStatus('mainStatus', `正在重新分析（${newDataLimit}天数据）...`);
                updateStatus('macdStatus', '重新计算中...');
                updateStatus('rsiStatus', '重新计算中...');
                updateStatus('mcsiMacdStatus', '重新计算中...');
                updateStatus('mcsiMmtStatus', '重新计算中...');
                updateStatus('mcsiRsiStatus', '重新计算中...');
                updateStatus('mcsiTtmStatus', '重新计算中...');

                // 重新加载数据（这会触发重新分析和图表重绘）
                loadStockData(currentStock.code);
            }
        });

        // 窗口大小调整 - 仅MCSI指标
        window.addEventListener('resize', function() {
            if (mainChart) {
                mainChart.applyOptions({
                    width: document.getElementById('mainChart').clientWidth,
                    height: document.getElementById('mainChart').clientHeight
                });
            }
            if (mcsiMacdChart) {
                mcsiMacdChart.applyOptions({
                    width: document.getElementById('mcsiMacdChart').clientWidth,
                    height: document.getElementById('mcsiMacdChart').clientHeight
                });
            }
            if (mcsiMmtChart) {
                mcsiMmtChart.applyOptions({
                    width: document.getElementById('mcsiMmtChart').clientWidth,
                    height: document.getElementById('mcsiMmtChart').clientHeight
                });
            }
            if (mcsiRsiChart) {
                mcsiRsiChart.applyOptions({
                    width: document.getElementById('mcsiRsiChart').clientWidth,
                    height: document.getElementById('mcsiRsiChart').clientHeight
                });
            }
            if (mcsiTtmChart) {
                mcsiTtmChart.applyOptions({
                    width: document.getElementById('mcsiTtmChart').clientWidth,
                    height: document.getElementById('mcsiTtmChart').clientHeight
                });
            }
        });
    </script>
</body>
</html>
