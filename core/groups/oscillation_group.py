#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
震荡分组
专门用于震荡相关的计分单元分组
"""

import pandas as pd
from typing import Dict, Any
from .base_group import BaseGroup, GroupResult


class OscillationGroup(BaseGroup):
    """
    震荡分组
    
    专门管理震荡相关的计分单元，如：
    - RSI计分单元
    - MACD计分单元
    - 波段计分单元
    - 其他震荡指标
    """
    
    def __init__(self,
                 group_id: str = 'oscillation_group',
                 name: str = '震荡分组',
                 description: str = '震荡相关指标的分组',
                 weight: float = 1.0):
        """
        初始化震荡分组
        
        Args:
            group_id: 分组唯一标识符
            name: 分组名称
            description: 分组描述
            weight: 分组权重
        """
        super().__init__(
            group_id=group_id,
            name=name,
            description=description,
            weight=weight
        )
        
        # 震荡分组特有配置
        self.config = {
            'oscillation_sensitivity': 0.7,  # 震荡敏感度
            'reversal_confirmation_period': 2,  # 反转确认周期
            'extreme_threshold': 0.8,  # 极值阈值
            'signal_filtering_enabled': True  # 是否启用信号过滤
        }
    
    def calculate_group_score(self, data: pd.DataFrame) -> GroupResult:
        """
        计算震荡分组分数
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            GroupResult: 震荡分组计分结果
        """
        try:
            unit_results = {}
            total_score = 0.0
            valid_unit_count = 0
            
            # 计算每个计分单元的分数
            for unit_id, unit in self.scoring_units.items():
                if unit.enabled:
                    try:
                        result = unit.score_with_validation(data)
                        unit_results[unit_id] = result.to_dict()
                        
                        if result.score != 0.0:  # 只计算有效分数
                            total_score += result.score
                            valid_unit_count += 1
                            
                    except Exception as e:
                        self.logger.warning(f"计分单元 {unit_id} 计算失败: {str(e)}")
                        unit_results[unit_id] = {
                            'score': 0.0,
                            'signal': 'neutral',
                            'description': f'计算失败: {str(e)}'
                        }
            
            # 计算加权分数（简化：直接使用加权平均作为最终分数）
            weighted_score = self.calculate_weighted_score(unit_results)

            # 计算置信度（基于有效单元数量）
            confidence = min(0.9, 0.3 + (valid_unit_count * 0.2))

            # 生成描述
            description = self._generate_oscillation_description(
                weighted_score, valid_unit_count, unit_results
            )

            return GroupResult(
                group_id=self.group_id,
                group_name=self.name,
                total_score=total_score,
                weighted_score=weighted_score,  # 直接使用加权分数
                unit_results=unit_results,
                confidence=confidence,
                description=description,
                metadata={
                    'valid_unit_count': valid_unit_count,
                    'calculation_method': 'simple_weighted_average',
                    'unit_weights': {unit_id: self.unit_weights.get(unit_id, 1.0)
                                   for unit_id in unit_results.keys()},
                    'config': self.config
                }
            )
            
        except Exception as e:
            self.logger.error(f"震荡分组计算失败: {str(e)}")
            return GroupResult(
                group_id=self.group_id,
                group_name=self.name,
                total_score=0.0,
                weighted_score=0.0,
                unit_results={},
                description=f'震荡分组计算错误: {str(e)}'
            )
    
    def _adjust_oscillation_score(self, weighted_score: float, 
                                 unit_results: Dict[str, Any], 
                                 data: pd.DataFrame) -> tuple:
        """
        震荡分组特有的分数调整
        
        Args:
            weighted_score: 原始加权分数
            unit_results: 单元结果字典
            data: 股票数据
            
        Returns:
            tuple: (调整后分数, 置信度)
        """
        adjusted_score = weighted_score
        confidence = 0.5
        
        try:
            # 1. 极值信号检测和增强（修复：使用更温和的调整）
            extreme_signals = self._detect_extreme_signals(unit_results)
            if extreme_signals['has_extreme']:
                # 极值信号增强 - 使用乘数而不是强制设置最小值
                strength = extreme_signals.get('strength', 0.0)
                signal_type = extreme_signals.get('type', 'neutral')

                if signal_type == 'oversold' and adjusted_score > 0:
                    # RSI超卖信号，适度增强正分数
                    adjusted_score *= (1.0 + strength * 0.3)  # 最多增强30%
                elif signal_type == 'overbought' and adjusted_score < 0:
                    # RSI超买信号，适度增强负分数
                    adjusted_score *= (1.0 + strength * 0.3)  # 最多增强30%
                elif signal_type in ['bullish', 'bearish']:
                    # MACD信号，轻微调整
                    if signal_type == 'bullish' and adjusted_score > 0:
                        adjusted_score *= (1.0 + strength * 0.2)  # 最多增强20%
                    elif signal_type == 'bearish' and adjusted_score < 0:
                        adjusted_score *= (1.0 + strength * 0.2)  # 最多增强20%

                confidence += 0.05 * strength  # 根据强度轻微增加置信度
            
            # 2. 信号一致性检查
            signal_consistency = self._check_signal_consistency(unit_results)
            confidence *= signal_consistency
            
            # 3. 反转信号过滤（修复：使用更温和的调整）
            if self.config.get('signal_filtering_enabled', True):
                reversal_strength = self._calculate_reversal_strength(data)
                if reversal_strength > 0.7:
                    # 强反转信号，轻微增强分数
                    adjusted_score *= 1.1  # 减少从1.3到1.1
                    confidence += 0.05
                elif reversal_strength < 0.3:
                    # 弱反转信号，轻微减弱分数
                    adjusted_score *= 0.9  # 减少从0.8到0.9
            
            # 4. 震荡敏感度调整
            sensitivity = self.config.get('oscillation_sensitivity', 0.7)
            adjusted_score *= sensitivity
            
            # 5. 限制分数范围
            max_score = 8.0
            min_score = -8.0
            adjusted_score = max(min_score, min(max_score, adjusted_score))
            
        except Exception as e:
            self.logger.warning(f"震荡分数调整失败: {str(e)}")
        
        return adjusted_score, confidence
    
    def _detect_extreme_signals(self, unit_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        检测极值信号
        
        Args:
            unit_results: 单元结果字典
            
        Returns:
            Dict[str, Any]: 极值信号信息
        """
        extreme_info = {
            'has_extreme': False,
            'type': 'neutral',
            'strength': 0.0
        }
        
        try:
            extreme_threshold = self.config.get('extreme_threshold', 0.8)
            
            for unit_id, result in unit_results.items():
                raw_value = result.get('raw_value')
                metadata = result.get('metadata', {})
                
                # RSI极值检测
                if 'rsi_value' in metadata:
                    rsi_value = metadata['rsi_value']
                    if rsi_value <= 20:  # 极度超卖
                        extreme_info['has_extreme'] = True
                        extreme_info['type'] = 'oversold'
                        extreme_info['strength'] = max(extreme_info['strength'], 
                                                     (30 - rsi_value) / 30)
                    elif rsi_value >= 80:  # 极度超买
                        extreme_info['has_extreme'] = True
                        extreme_info['type'] = 'overbought'
                        extreme_info['strength'] = max(extreme_info['strength'], 
                                                     (rsi_value - 70) / 30)
                
                # MACD极值检测（修复：使用更合理的阈值和逻辑）
                if 'histogram' in metadata:
                    histogram = metadata['histogram']
                    # 使用更高的阈值，避免误判
                    if abs(histogram) > 5.0:  # 提高阈值到5.0
                        extreme_info['has_extreme'] = True
                        # 修复：正确的MACD信号判断
                        # 正的柱状图表示上涨动能，负的表示下跌动能
                        extreme_info['type'] = 'bullish' if histogram > 0 else 'bearish'
                        extreme_info['strength'] = max(extreme_info['strength'],
                                                     min(1.0, abs(histogram) / 10.0))
        
        except Exception as e:
            self.logger.warning(f"极值信号检测失败: {str(e)}")
        
        return extreme_info
    
    def _check_signal_consistency(self, unit_results: Dict[str, Any]) -> float:
        """
        检查信号一致性
        
        Args:
            unit_results: 单元结果字典
            
        Returns:
            float: 一致性系数 (0.0-1.0)
        """
        try:
            signals = []
            for unit_id, result in unit_results.items():
                signal = result.get('signal', 'neutral')
                if 'bullish' in signal:
                    signals.append(1)
                elif 'bearish' in signal:
                    signals.append(-1)
                else:
                    signals.append(0)
            
            if not signals:
                return 0.5
            
            # 计算信号的标准差，标准差越小，一致性越高
            import numpy as np
            signal_std = np.std(signals)
            
            # 将标准差转换为一致性系数
            consistency = max(0.3, 1.0 - signal_std / 2.0)
            
            return consistency
            
        except Exception:
            return 0.5
    
    def _calculate_reversal_strength(self, data: pd.DataFrame) -> float:
        """
        计算反转强度
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            float: 反转强度 (0.0-1.0)
        """
        try:
            if len(data) < 10:
                return 0.5
            
            # 使用最近几天的价格变化来判断反转强度
            recent_data = data.tail(5)
            prices = recent_data['close'].values
            
            # 计算价格变化的方向变化
            price_changes = [prices[i] - prices[i-1] for i in range(1, len(prices))]
            
            # 检测方向变化
            direction_changes = 0
            for i in range(1, len(price_changes)):
                if (price_changes[i] > 0) != (price_changes[i-1] > 0):
                    direction_changes += 1
            
            # 方向变化越多，反转强度越高
            reversal_strength = min(1.0, direction_changes / (len(price_changes) - 1))
            
            return reversal_strength
            
        except Exception:
            return 0.5
    
    def _generate_oscillation_description(self, score: float, valid_count: int, 
                                         unit_results: Dict[str, Any]) -> str:
        """
        生成震荡描述
        
        Args:
            score: 调整后分数
            valid_count: 有效单元数量
            unit_results: 单元结果字典
            
        Returns:
            str: 震荡描述
        """
        if score >= 3.0:
            osc_desc = "强烈超卖反弹信号"
        elif score >= 1.5:
            osc_desc = "超卖反弹信号"
        elif score >= 0.5:
            osc_desc = "轻微看涨信号"
        elif score <= -3.0:
            osc_desc = "强烈超买回调信号"
        elif score <= -1.5:
            osc_desc = "超买回调信号"
        elif score <= -0.5:
            osc_desc = "轻微看跌信号"
        else:
            osc_desc = "震荡中性"
        
        return f"震荡分析({score:.2f}): {osc_desc} (基于{valid_count}个指标)"
    
    def add_default_units(self):
        """添加默认的震荡计分单元（包含高级MCSI指标）"""
        try:
            from core.scoring_units import RSIScoringUnit, MACDScoringUnit, WaveScoringUnit

            # 添加传统RSI计分单元
            rsi_unit = RSIScoringUnit(
                unit_id='rsi_unit',
                name='RSI计分单元',
                description='基于RSI指标的计分单元',
                period=14,
                oversold_threshold=30.0,
                overbought_threshold=70.0
            )
            self.add_scoring_unit(rsi_unit, weight=1.0)

            # 添加传统MACD计分单元
            macd_unit = MACDScoringUnit(
                unit_id='macd_unit',
                name='MACD计分单元',
                description='基于MACD指标的计分单元'
            )
            self.add_scoring_unit(macd_unit, weight=0.8)

            # 添加波段计分单元
            wave_unit = WaveScoringUnit(
                unit_id='wave_unit',
                name='波段计分单元',
                description='基于波段分析的计分单元'
            )
            self.add_scoring_unit(wave_unit, weight=0.6)
            
            # 添加高级MCSI计分单元（从配置文件读取是否启用）
            try:
                import json
                with open('config/scoring_unit_config.json', 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 检查高级单元配置
                premium_units_config = {
                    'mcsi_premium_rsi_unit': ('MCSIPremiumRSIUnit', 1.2),
                    'mcsi_premium_macd_unit': ('MCSIPremiumMACDUnit', 1.1),
                    'mcsi_premium_mmt_unit': ('MCSIPremiumMMTUnit', 1.0),
                    'mcsi_premium_ttm_unit': ('MCSIPremiumTTMUnit', 1.1)
                }
                
                # 尝试导入高级计分单元
                from core.scoring_units.mcsi_premium_units import (
                    MCSIPremiumRSIUnit,
                    MCSIPremiumMACDUnit, 
                    MCSIPremiumMMTUnit,
                    MCSIPremiumTTMUnit,
                    MCSI_PREMIUM_AVAILABLE
                )
                
                if MCSI_PREMIUM_AVAILABLE:
                    premium_classes = {
                        'MCSIPremiumRSIUnit': MCSIPremiumRSIUnit,
                        'MCSIPremiumMACDUnit': MCSIPremiumMACDUnit,
                        'MCSIPremiumMMTUnit': MCSIPremiumMMTUnit,
                        'MCSIPremiumTTMUnit': MCSIPremiumTTMUnit
                    }
                    
                    for unit_config_id, (class_name, default_weight) in premium_units_config.items():
                        if (unit_config_id in config.get('scoring_units', {}) and 
                            config['scoring_units'][unit_config_id].get('enabled', False)):
                            
                            # 创建高级单元实例
                            unit_class = premium_classes[class_name]
                            premium_unit = unit_class()
                            
                            # 获取配置的权重，如果没有则使用默认权重
                            weight = config.get('default_weights', {}).get(unit_config_id, default_weight)
                            
                            # 添加到分组
                            self.add_scoring_unit(premium_unit, weight=weight)
                            
                            self.logger.info(f"✅ 添加高级计分单元: {premium_unit.name} (权重: {weight})")
                    
                    self.logger.info(f"🚀 MCSI高级计分单元集成成功")
                else:
                    self.logger.warning("⚠️ MCSI高级组件不可用，跳过高级单元加载")
                    
            except ImportError as e:
                self.logger.warning(f"⚠️ 无法导入MCSI高级计分单元: {e}")
            except Exception as e:
                self.logger.warning(f"⚠️ 高级计分单元配置加载失败: {e}")

            self.logger.info("已添加默认震荡计分单元（包含高级MCSI指标）")

        except ImportError as e:
            self.logger.warning(f"无法导入默认震荡计分单元: {str(e)}")
        except Exception as e:
            self.logger.error(f"默认震荡分组初始化失败: {str(e)}")
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """
        更新震荡分组配置
        
        Args:
            config: 新的配置参数
        """
        super().update_config(config)
        
        # 震荡分组特有配置更新
        if 'oscillation_sensitivity' in config:
            self.config['oscillation_sensitivity'] = config['oscillation_sensitivity']
        
        if 'reversal_confirmation_period' in config:
            self.config['reversal_confirmation_period'] = config['reversal_confirmation_period']
        
        if 'extreme_threshold' in config:
            self.config['extreme_threshold'] = config['extreme_threshold']
        
        if 'signal_filtering_enabled' in config:
            self.config['signal_filtering_enabled'] = config['signal_filtering_enabled']
