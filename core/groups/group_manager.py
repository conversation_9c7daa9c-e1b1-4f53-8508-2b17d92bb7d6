#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分组管理器
管理所有计分单元分组的核心类
"""

from typing import Dict, List, Any, Optional
import logging
import json
import os
import pandas as pd
from .base_group import BaseGroup, GroupResult
from .trend_group import TrendGroup
from .oscillation_group import OscillationGroup
from .custom_group import CustomGroup
from ..config.weight_config_manager import WeightConfigManager


class GroupManager:
    """
    分组管理器
    
    负责管理所有计分单元分组，包括：
    - 分组的创建、删除、配置
    - 分组权重管理
    - 分组计分协调
    - 配置的保存和加载
    """
    
    def __init__(self, config_file: str = 'config/group_config.json'):
        """
        初始化分组管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        self.config_file = config_file
        
        # 分组存储
        self.groups: Dict[str, BaseGroup] = {}
        self.group_weights: Dict[str, float] = {}

        # 权重配置管理器
        self.weight_config_manager = WeightConfigManager()

        # 默认配置
        self.default_config = {
            'default_groups': {
                'trend_group': {
                    'enabled': True,
                    'weight': 0.7,
                    'config': {}
                },
                'oscillation_group': {
                    'enabled': True,
                    'weight': 0.3,
                    'config': {}
                }
            },
            'global_settings': {
                'normalize_scores': True,
                'min_valid_groups': 1,
                'confidence_threshold': 0.3
            }
        }
        
        # 加载配置
        self.load_config()
        
        # 初始化默认分组
        self.initialize_default_groups()
    
    def initialize_default_groups(self) -> None:
        """初始化默认分组"""
        try:
            # 创建趋势分组
            if 'trend_group' not in self.groups:
                trend_group = TrendGroup()
                trend_group.add_default_units()
                self.add_group(trend_group, weight=0.7)
            
            # 创建震荡分组
            if 'oscillation_group' not in self.groups:
                oscillation_group = OscillationGroup()
                oscillation_group.add_default_units()
                self.add_group(oscillation_group, weight=0.3)
            
            self.logger.info("默认分组初始化完成")
            
        except Exception as e:
            self.logger.error(f"默认分组初始化失败: {str(e)}")
    
    def add_group(self, group: BaseGroup, weight: float = 1.0) -> None:
        """
        添加分组

        Args:
            group: 分组实例
            weight: 分组权重
        """
        self.groups[group.group_id] = group
        self.group_weights[group.group_id] = weight

        # 设置回调函数，让分组能够通知管理器保存配置
        group._manager_callback = self.save_config

        self.logger.info(f"添加分组: {group.group_id} (权重: {weight})")
    
    def remove_group(self, group_id: str) -> None:
        """
        移除分组
        
        Args:
            group_id: 分组ID
        """
        if group_id in self.groups:
            del self.groups[group_id]
            del self.group_weights[group_id]
            self.logger.info(f"移除分组: {group_id}")
    
    def get_group(self, group_id: str) -> Optional[BaseGroup]:
        """
        获取分组
        
        Args:
            group_id: 分组ID
            
        Returns:
            Optional[BaseGroup]: 分组实例，如果不存在返回None
        """
        return self.groups.get(group_id)
    
    def list_groups(self) -> List[Dict[str, Any]]:
        """
        列出所有分组
        
        Returns:
            List[Dict[str, Any]]: 分组信息列表
        """
        group_list = []
        for group_id, group in self.groups.items():
            group_info = group.get_info()
            group_info['weight'] = self.group_weights.get(group_id, 1.0)
            group_list.append(group_info)
        return group_list
    
    def update_group_weight(self, group_id: str, weight: float, auto_save: bool = True) -> None:
        """
        更新分组权重

        Args:
            group_id: 分组ID
            weight: 新权重
            auto_save: 是否自动保存配置
        """
        if group_id in self.group_weights:
            self.group_weights[group_id] = weight
            self.logger.info(f"更新分组 {group_id} 权重为 {weight}")

            # 自动保存配置
            if auto_save:
                self.save_config()
    
    def set_group_weights(self, weights: Dict[str, float], auto_save: bool = True) -> None:
        """
        批量设置分组权重

        Args:
            weights: 权重字典 {group_id: weight}
            auto_save: 是否自动保存配置
        """
        for group_id, weight in weights.items():
            if group_id in self.groups:
                self.group_weights[group_id] = weight
        self.logger.info("批量更新分组权重")

        # 自动保存配置
        if auto_save:
            self.save_config()
    
    def get_group_weights(self) -> Dict[str, float]:
        """获取所有分组权重"""
        return self.group_weights.copy()
    
    def create_custom_group(self, group_id: str, name: str, 
                           description: str = '', weight: float = 1.0,
                           custom_config: Dict[str, Any] = None) -> CustomGroup:
        """
        创建自定义分组
        
        Args:
            group_id: 分组ID
            name: 分组名称
            description: 分组描述
            weight: 分组权重
            custom_config: 自定义配置
            
        Returns:
            CustomGroup: 创建的自定义分组
        """
        if group_id in self.groups:
            raise ValueError(f"分组 {group_id} 已存在")
        
        custom_group = CustomGroup(
            group_id=group_id,
            name=name,
            description=description,
            weight=weight,
            custom_config=custom_config
        )
        
        self.add_group(custom_group, weight)
        self.logger.info(f"创建自定义分组: {group_id}")
        
        return custom_group
    
    def calculate_all_groups(self, data: pd.DataFrame) -> Dict[str, GroupResult]:
        """
        计算所有分组的分数
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            Dict[str, GroupResult]: 所有分组的计分结果
        """
        group_results = {}
        
        for group_id, group in self.groups.items():
            if group.enabled:
                try:
                    result = group.score_with_validation(data)
                    group_results[group_id] = result
                    self.logger.debug(f"分组 {group_id} 计分完成: {result.weighted_score:.2f}")
                except Exception as e:
                    self.logger.error(f"分组 {group_id} 计分失败: {str(e)}")
        
        return group_results
    
    def calculate_composite_score(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        计算综合分数
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            Dict[str, Any]: 综合分数结果
        """
        try:
            # 计算所有分组分数
            group_results = self.calculate_all_groups(data)
            
            if not group_results:
                return {
                    'composite_score': 0.0,
                    'confidence': 0.0,
                    'description': '没有可用的分组',
                    'group_results': {},
                    'group_weights': {}
                }
            
            # 计算加权综合分数
            total_weighted_score = 0.0
            total_weight = 0.0
            total_confidence = 0.0
            valid_groups = 0
            
            for group_id, result in group_results.items():
                weight = self.group_weights.get(group_id, 1.0)
                total_weighted_score += result.weighted_score * weight
                total_weight += weight
                total_confidence += result.confidence
                valid_groups += 1
            
            # 计算最终分数
            if total_weight > 0:
                composite_score = total_weighted_score / total_weight
                avg_confidence = total_confidence / valid_groups if valid_groups > 0 else 0.0
            else:
                composite_score = 0.0
                avg_confidence = 0.0
            
            # 生成描述
            description = self._generate_composite_description(
                composite_score, valid_groups, group_results
            )
            
            return {
                'composite_score': composite_score,
                'confidence': avg_confidence,
                'description': description,
                'group_results': {gid: result.to_dict() for gid, result in group_results.items()},
                'group_weights': self.group_weights,
                'valid_groups': valid_groups
            }
            
        except Exception as e:
            self.logger.error(f"综合分数计算失败: {str(e)}")
            return {
                'composite_score': 0.0,
                'confidence': 0.0,
                'description': f'计算错误: {str(e)}',
                'group_results': {},
                'group_weights': {}
            }
    
    def _generate_composite_description(self, score: float, valid_groups: int,
                                       group_results: Dict[str, GroupResult]) -> str:
        """
        生成综合描述
        
        Args:
            score: 综合分数
            valid_groups: 有效分组数量
            group_results: 分组结果字典
            
        Returns:
            str: 综合描述
        """
        if score >= 4.0:
            grade = "A+"
            desc = "强烈推荐"
        elif score >= 3.0:
            grade = "A"
            desc = "推荐"
        elif score >= 2.0:
            grade = "B"
            desc = "较好"
        elif score >= 1.0:
            grade = "C"
            desc = "一般"
        elif score >= 0:
            grade = "D"
            desc = "观望"
        elif score >= -2.0:
            grade = "E"
            desc = "谨慎"
        else:
            grade = "F"
            desc = "回避"
        
        return f"综合评分({score:.2f}): {grade}级 - {desc} (基于{valid_groups}个分组)"
    
    def save_config(self) -> None:
        """保存配置到文件"""
        try:
            config_data = {
                'groups': {},
                'group_weights': self.group_weights,
                'global_settings': self.default_config['global_settings']
            }

            # 收集计分单元权重
            unit_weights = {}

            # 保存每个分组的配置
            for group_id, group in self.groups.items():
                config_data['groups'][group_id] = {
                    'type': type(group).__name__,
                    'enabled': group.enabled,
                    'config': group.get_config(),
                    'unit_weights': group.get_unit_weights()
                }

                # 收集单元权重用于权重配置管理器
                unit_weights[group_id] = group.get_unit_weights()

            # 确保配置目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)

            # 保存配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)

            # 同时保存到权重配置管理器
            self.weight_config_manager.save_weight_config(
                group_weights=self.group_weights,
                unit_weights=unit_weights,
                auto_backup=True
            )
            
            self.logger.info(f"配置已保存到 {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {str(e)}")
    
    def load_config(self) -> None:
        """从文件加载配置"""
        try:
            # 首先尝试从权重配置管理器加载
            self.weight_config_manager.load_config()
            saved_group_weights = self.weight_config_manager.get_group_weights()
            saved_unit_weights = self.weight_config_manager.get_unit_weights()

            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                # 优先使用权重配置管理器的权重，如果没有则使用配置文件的
                self.group_weights = saved_group_weights if saved_group_weights else config_data.get('group_weights', {})

                # 加载分组内的计分单元权重
                groups_config = config_data.get('groups', {})
                for group_id, group_config in groups_config.items():
                    if group_id in self.groups:
                        # 优先使用权重配置管理器的单元权重
                        if group_id in saved_unit_weights:
                            unit_weights = saved_unit_weights[group_id]
                        else:
                            unit_weights = group_config.get('unit_weights', {})

                        if unit_weights:
                            self.groups[group_id].set_unit_weights(unit_weights, notify_manager=False)
                            self.logger.info(f"重新加载分组 {group_id} 的计分单元权重: {unit_weights}")

                # 加载全局设置
                if 'global_settings' in config_data:
                    self.default_config['global_settings'].update(
                        config_data['global_settings']
                    )

                self.logger.info(f"配置已从 {self.config_file} 加载")
            else:
                # 如果配置文件不存在但权重配置管理器有数据，则使用权重配置管理器的数据
                if saved_group_weights:
                    self.group_weights = saved_group_weights
                    self.logger.info("从权重配置管理器加载分组权重")

                if saved_unit_weights:
                    for group_id, unit_weights in saved_unit_weights.items():
                        if group_id in self.groups:
                            self.groups[group_id].set_unit_weights(unit_weights, notify_manager=False)
                    self.logger.info("从权重配置管理器加载计分单元权重")

                if not saved_group_weights and not saved_unit_weights:
                    self.logger.info("配置文件不存在，使用默认配置")

        except Exception as e:
            self.logger.error(f"加载配置失败: {str(e)}")
    
    def get_available_scoring_units(self) -> List[str]:
        """
        获取所有可用的计分单元类型（包含高级MCSI指标）

        Returns:
            List[str]: 可用的计分单元类型列表
        """
        try:
            # 基础计分单元
            from core.scoring_units import (
                RSIScoringUnit, MACDScoringUnit,
                TrendScoringUnit, WaveScoringUnit,
                MCSIMACDScoringUnit, MCSIMMTScoringUnit,
                MCSIRSIScoringUnit, MCSITD9ScoringUnit
            )

            units = [
                'RSIScoringUnit',
                'MACDScoringUnit',
                'TrendScoringUnit',
                'WaveScoringUnit',
                'MCSIMACDScoringUnit',
                'MCSIMMTScoringUnit',
                'MCSIRSIScoringUnit',
                'MCSITD9ScoringUnit'
            ]
            
            # 尝试添加高级MCSI计分单元
            try:
                from core.scoring_units.mcsi_premium_units import (
                    MCSIPremiumRSIUnit,
                    MCSIPremiumMACDUnit, 
                    MCSIPremiumMMTUnit,
                    MCSIPremiumTTMUnit,
                    MCSI_PREMIUM_AVAILABLE
                )
                
                if MCSI_PREMIUM_AVAILABLE:
                    premium_units = [
                        'MCSIPremiumRSIUnit',
                        'MCSIPremiumMACDUnit',
                        'MCSIPremiumMMTUnit', 
                        'MCSIPremiumTTMUnit'
                    ]
                    units.extend(premium_units)
                    self.logger.info(f"✅ 添加{len(premium_units)}个MCSI高级计分单元到API列表")
                else:
                    self.logger.warning("⚠️ MCSI高级组件不可用")
                    
            except ImportError as e:
                self.logger.warning(f"⚠️ 无法导入MCSI高级计分单元: {e}")
            
            return units
            
        except ImportError as e:
            self.logger.warning(f"导入计分单元失败: {str(e)}")
            return []
