#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境配置检查脚本
验证Analyze-system2的环境配置是否完整
"""

import sys
import os
from pathlib import Path

def check_python_path():
    """检查Python路径配置"""
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    print(f"✓ 项目根目录: {project_root}")
    return project_root

def check_core_modules():
    """检查核心模块是否可导入"""
    try:
        from core.composite.scorer import NewCompositeScorer
        print("✓ 综合评分器模块导入成功")
    except ImportError as e:
        print(f"✗ 综合评分器模块导入失败: {e}")
        return False
    
    try:
        from core.data.hybrid_data_loader import HybridDataLoader
        print("✓ 混合数据加载器模块导入成功")
    except ImportError as e:
        print(f"✗ 混合数据加载器模块导入失败: {e}")
        return False
        
    try:
        from core.scoring_units.mcsi_unified_direct import MCSIUnifiedMACDUnit
        print("✓ MCSI统一接口模块导入成功")
    except ImportError as e:
        print(f"✗ MCSI统一接口模块导入失败: {e}")
        return False
    
    # 检查是否有残留的Cython/Premium引用
    try:
        import os
        import glob
        cython_files = glob.glob("**/*cython*", recursive=True) + glob.glob("**/*.so", recursive=True) + glob.glob("**/*premium*", recursive=True)
        if cython_files:
            print(f"⚠ 发现残留的Cython/Premium文件: {cython_files}")
        else:
            print("✓ 已清理所有Cython/Premium相关文件")
    except Exception as e:
        print(f"! Cython清理检查失败: {e}")
        
    return True

def check_config_files():
    """检查配置文件"""
    required_configs = [
        'config/settings.py',
        'config/scoring_unit_config.json',
        'config/group_config.json',
        '.env'
    ]
    
    all_exist = True
    for config in required_configs:
        if os.path.exists(config):
            print(f"✓ 配置文件存在: {config}")
        else:
            print(f"✗ 配置文件缺失: {config}")
            all_exist = False
    
    return all_exist

def check_data_directories():
    """检查数据目录"""
    required_dirs = [
        'stock_data',
        'logs',
        'TV-code/py-code'
    ]
    
    all_exist = True
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"✓ 目录存在: {directory}")
        else:
            print(f"✗ 目录缺失: {directory}")
            all_exist = False
    
    return all_exist

def check_sample_data():
    """检查样本数据"""
    sample_files = [
        'stock_data/cnindex_000001_上证指数.csv',
        'stock_data/ashares_600519_贵州茅台.csv'
    ]
    
    data_available = False
    for sample_file in sample_files:
        if os.path.exists(sample_file):
            print(f"✓ 样本数据存在: {sample_file}")
            data_available = True
        else:
            print(f"! 样本数据缺失: {sample_file} (系统将使用数据库)")
    
    return data_available

def check_web_app():
    """检查Web应用是否可启动"""
    try:
        from web.app import app, init_scorer
        print("✓ Web应用模块导入成功")
        
        # 尝试初始化评分器（但不启动服务）
        print("⚠ 注意: 评分器初始化需要数据库连接，跳过实际初始化测试")
        return True
    except ImportError as e:
        print(f"✗ Web应用模块导入失败: {e}")
        return False

def main():
    """主检查函数"""
    print("=" * 60)
    print("🔧 Analyze-system2 环境配置检查")
    print("=" * 60)
    
    # 检查Python路径
    project_root = check_python_path()
    os.chdir(project_root)
    
    print("\n📦 核心模块检查:")
    modules_ok = check_core_modules()
    
    print("\n📄 配置文件检查:")
    configs_ok = check_config_files()
    
    print("\n📁 目录结构检查:")
    dirs_ok = check_data_directories()
    
    print("\n📊 样本数据检查:")
    data_ok = check_sample_data()
    
    print("\n🌐 Web应用检查:")
    web_ok = check_web_app()
    
    print("\n" + "=" * 60)
    print("📋 检查结果总结:")
    print(f"  • 核心模块: {'✓ 正常' if modules_ok else '✗ 异常'}")
    print(f"  • 配置文件: {'✓ 完整' if configs_ok else '✗ 不完整'}")
    print(f"  • 目录结构: {'✓ 完整' if dirs_ok else '✗ 不完整'}")
    print(f"  • 样本数据: {'✓ 可用' if data_ok else '! 依赖数据库'}")
    print(f"  • Web应用: {'✓ 就绪' if web_ok else '✗ 异常'}")
    
    if all([modules_ok, configs_ok, dirs_ok, web_ok]):
        print("\n🎉 环境配置完整，系统可以启动！")
        print("\n启动命令:")
        print("  python main.py          # 生产模式")
        print("  python dev.py           # 开发模式")
        print("  ./start_service.sh start # 后台服务")
        return True
    else:
        print("\n⚠️  环境配置存在问题，需要修复后再启动")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)