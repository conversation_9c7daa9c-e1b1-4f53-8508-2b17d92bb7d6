#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI计分单元智能适配器
优先加载标准化ScoringUnit，失败时使用适配器版本
集成DataProvider支持混合数据输入
"""

import sys
import os
import importlib.util
import pandas as pd
import numpy as np
from typing import List, Optional, Any, Dict, Union, Tuple
import logging

# 添加TV-code路径
tv_code_path = os.path.join(os.path.dirname(__file__), '../../TV-code/py-code')
if tv_code_path not in sys.path:
    sys.path.insert(0, tv_code_path)

try:
    from .base_scoring_unit import BaseScoringUnit, ScoringResult
except ImportError:
    from base_scoring_unit import BaseScoringUnit, ScoringResult

# 尝试导入DataProvider
try:
    from .data_provider import DataProvider
except ImportError:
    DataProvider = None
    
logger = logging.getLogger(__name__)

class EnvironmentConfig:
    """环境变量配置管理"""
    
    @staticmethod
    def get_db_connection_string(fallback: Optional[str] = None) -> str:
        """安全获取数据库连接字符串"""
        # 优先级: DB_CONN -> DATABASE_URL -> 传入的fallback -> 默认值
        db_conn = os.environ.get('DB_CONN')
        if db_conn:
            return db_conn
            
        db_conn = os.environ.get('DATABASE_URL')  
        if db_conn:
            return db_conn
            
        if fallback:
            return fallback
            
        # 默认连接字符串（开发环境）
        return '************************************************/fintech_db'
    
    @staticmethod
    def get_cache_config() -> Dict[str, Any]:
        """获取缓存配置"""
        return {
            'enabled': os.environ.get('CACHE_ENABLED', 'true').lower() == 'true',
            'size': int(os.environ.get('CACHE_SIZE', '128')),
            'ttl': int(os.environ.get('CACHE_TTL', '3600'))
        }

class MCSIAdapter:
    """MCSI计分单元适配器 - 支持混合数据输入"""
    
    # 数据提供器实例（共享）
    _data_provider = None
    
    @classmethod
    def get_data_provider(cls) -> Optional[DataProvider]:
        """获取数据提供器实例（单例模式）"""
        if cls._data_provider is None and DataProvider is not None:
            cls._data_provider = DataProvider(cache_enabled=True)
        return cls._data_provider
    
    @staticmethod
    def validate_period_parameter(period: str) -> str:
        """验证period参数"""
        valid_periods = ['daily', 'weekly', 'monthly']
        if period not in valid_periods:
            logger.warning(f"无效的period参数: {period}, 使用默认值 'daily'")
            return 'daily'
        return period
    
    @staticmethod
    def create_error_result(error_msg: str, error_code: str = 'unknown_error') -> ScoringResult:
        """创建错误结果"""
        return ScoringResult(
            score=0.0,
            signal='neutral',
            confidence=0.0,
            description=f'错误: {error_msg}',
            metadata={
                'error': True,
                'error_code': error_code,
                'error_message': error_msg,
                'rows_processed': 0,
                'data_source': 'error',
                'api_version': '3.8_optimized'
            }
        )
    
    @staticmethod
    def prepare_data(data_source: Union[pd.DataFrame, Dict[str, Any]],
                    symbol: Optional[str] = None,
                    period: str = 'daily') -> Tuple[pd.DataFrame, Optional[Dict]]:
        """
        准备数据，支持多种输入格式和时间框架
        
        Args:
            data_source: DataFrame或包含数据库连接信息的字典
            symbol: 股票代码
            period: 时间框架 ('daily', 'weekly', 'monthly')
            
        Returns:
            (DataFrame, seasonal_factors)
        """
        # 如果已经是DataFrame，直接返回
        if isinstance(data_source, pd.DataFrame):
            return data_source, None
            
        # 如果是字典，使用DataProvider获取数据
        if isinstance(data_source, dict) and DataProvider is not None:
            provider = MCSIAdapter.get_data_provider()
            if provider:
                # 安全处理数据库连接字符串
                db_conn = EnvironmentConfig.get_db_connection_string(
                    data_source.get('db_conn')
                )
                
                # 验证period参数
                data_period = MCSIAdapter.validate_period_parameter(
                    data_source.get('period', period)
                )
                
                df = provider.get_stock_data(
                    db_conn=db_conn,
                    symbol=symbol or data_source.get('symbol'),
                    start_date=data_source.get('start_date'),
                    end_date=data_source.get('end_date'),
                    period=data_period,  # 新增：支持period参数
                    ohlc=data_source.get('ohlc'),
                    include_seasonal=data_source.get('include_seasonal', False)
                )
                
                # 返回季节性因子（如果存在）
                seasonal_factors = data_source.get('seasonal_factors')
                return df, seasonal_factors
        
        # 默认返回空DataFrame
        return pd.DataFrame(), None
    
    @staticmethod
    def load_protected_module(module_name: str, cpp_file_path: str) -> Optional[Any]:
        """尝试加载保护的模块（.cpp文件作为.pyc）"""
        try:
            # 方法1: 尝试直接导入.cpp文件作为Python模块
            spec = importlib.util.spec_from_file_location(module_name, cpp_file_path)
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                return module
        except Exception as e:
            logger.debug(f"方法1加载失败: {e}")
        
        try:
            # 方法2: 尝试读取并执行.cpp文件内容
            with open(cpp_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 创建模块对象
            module = type(sys)('temp_module')
            module.__file__ = cpp_file_path
            
            # 执行代码
            exec(compile(content, cpp_file_path, 'exec'), module.__dict__)
            return module
        except Exception as e:
            logger.debug(f"方法2加载失败: {e}")
        
        return None
    
    @staticmethod
    def create_mcsi_macd_unit():
        """创建MCSI MACD计分单元 - 返回支持混合输入的包装器"""
        logger.info("✅ 创建MCSI MACD混合输入包装器")
        return MCSIMACDScoringUnitWrapper()
    
    @staticmethod
    def create_mcsi_mmt_unit():
        """创建MCSI MMT计分单元 - 返回支持混合输入的包装器"""
        logger.info("✅ 创建MCSI MMT混合输入包装器")
        return MCSIMMTScoringUnitWrapper()
    
    @staticmethod
    def create_mcsi_rsi_unit():
        """创建MCSI RSI计分单元 - 返回支持混合输入的包装器"""
        logger.info("✅ 创建MCSI RSI混合输入包装器")
        return MCSIRSIScoringUnitWrapper()
    
    @staticmethod
    def create_mcsi_ttm_unit():
        """创建MCSI TTM计分单元 - 返回支持混合输入的包装器"""
        logger.info("✅ 创建MCSI TTM混合输入包装器")
        return MCSITTMScoringUnitWrapper()


class MCSIMACDScoringUnitWrapper(BaseScoringUnit):
    """MCSI MACD计分单元包装器 - 支持混合数据输入"""
    
    def __init__(self):
        super().__init__(
            unit_id='mcsi_macd_wrapper',
            name='MCSI MACD包装器',
            description='支持混合数据输入的MCSI MACD包装器',
            min_score=-100.0,
            max_score=100.0
        )
        self._standard_unit = None
        self._adapter_unit = None
    
    def _get_standard_unit(self):
        """获取标准化单元"""
        if self._standard_unit is None:
            try:
                from .mcsi_macd_scoring import MCSIMACDScoringUnit as StandardUnit
                self._standard_unit = StandardUnit()
            except ImportError:
                pass
        return self._standard_unit
    
    def _get_adapter_unit(self):
        """获取适配器单元"""
        if self._adapter_unit is None:
            self._adapter_unit = MCSIMACDScoringUnitAdapter()
        return self._adapter_unit
    
    def calculate_score(self, data: Union[pd.DataFrame, Dict[str, Any]], 
                      seasonal_factors: Optional[Dict] = None, period: str = 'daily') -> ScoringResult:
        """计算分数，支持混合数据输入"""
        # 记住原始数据类型用于metadata
        is_dict_input = isinstance(data, dict)
        
        # 提取期间参数
        original_period = data.get('period', period) if is_dict_input else period
        
        # 如果传入的是字典格式，使用DataProvider处理
        if is_dict_input:
            data, seasonal_factors = MCSIAdapter.prepare_data(data, period=original_period)
        
        if data.empty:
            return ScoringResult(score=0.0, signal='neutral', confidence=0.0,
                               description='无有效数据', 
                               metadata={
                                   'error': 'empty_data',
                                   'rows_processed': 0,
                                   'data_source': 'database' if is_dict_input else 'dataframe',
                                   'period_used': original_period,
                                   'period_support': True,
                                   'api_version': '3.8_optimized'
                               })
        
        # 优先使用标准化单元
        standard_unit = self._get_standard_unit()
        if standard_unit:
            try:
                result = standard_unit.calculate_score(data, seasonal_factors)
                result.metadata.update({
                    'unit_type': 'standard',
                    'rows_processed': len(data),
                    'data_source': 'database' if is_dict_input else 'dataframe',
                    'period_used': original_period,
                    'period_support': True,
                    'api_version': '3.8_optimized'
                })
                return result
            except Exception as e:
                logger.warning(f"标准化单元计算失败: {e}，回退到适配器")
        
        # 回退到适配器单元
        adapter_unit = self._get_adapter_unit()
        result = adapter_unit.calculate_score(data, seasonal_factors)
        result.metadata.update({
            'unit_type': 'adapter',
            'rows_processed': len(data),
            'data_source': 'database' if is_dict_input else 'dataframe',
            'period_used': original_period,
            'period_support': True,
            'api_version': '3.8_optimized'
        })
        return result
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        standard_unit = self._get_standard_unit()
        if standard_unit:
            return standard_unit.validate_data(data)
        else:
            return self._get_adapter_unit().validate_data(data)
    
    def get_required_columns(self) -> List[str]:
        standard_unit = self._get_standard_unit()
        if standard_unit:
            return standard_unit.get_required_columns()
        else:
            return self._get_adapter_unit().get_required_columns()
    
    def get_min_data_points(self) -> int:
        standard_unit = self._get_standard_unit()
        if standard_unit:
            return standard_unit.get_min_data_points()
        else:
            return self._get_adapter_unit().get_min_data_points()


class MCSIMMTScoringUnitWrapper(BaseScoringUnit):
    """MCSI MMT计分单元包装器 - 支持混合数据输入"""
    
    def __init__(self):
        super().__init__(
            unit_id='mcsi_mmt_wrapper',
            name='MCSI MMT包装器',
            description='支持混合数据输入的MCSI MMT包装器',
            min_score=-100.0,
            max_score=100.0
        )
        self._standard_unit = None
        self._adapter_unit = None
    
    def _get_standard_unit(self):
        """获取标准化单元"""
        if self._standard_unit is None:
            try:
                from .mcsi_mmt_scoring import MCSIMMTScoringUnit as StandardUnit
                self._standard_unit = StandardUnit()
            except ImportError:
                pass
        return self._standard_unit
    
    def _get_adapter_unit(self):
        """获取适配器单元"""
        if self._adapter_unit is None:
            self._adapter_unit = MCSIMMTScoringUnitAdapter()
        return self._adapter_unit
    
    def calculate_score(self, data: Union[pd.DataFrame, Dict[str, Any]], 
                      seasonal_factors: Optional[Dict] = None, period: str = 'daily') -> ScoringResult:
        """计算分数，支持混合数据输入"""
        # 记住原始数据类型用于metadata
        is_dict_input = isinstance(data, dict)
        
        # 提取期间参数
        original_period = data.get('period', period) if is_dict_input else period
        
        # 如果传入的是字典格式，使用DataProvider处理
        if is_dict_input:
            data, seasonal_factors = MCSIAdapter.prepare_data(data, period=original_period)
        
        if data.empty:
            return ScoringResult(score=0.0, signal='neutral', confidence=0.0,
                               description='无有效数据', 
                               metadata={
                                   'error': 'empty_data',
                                   'rows_processed': 0,
                                   'data_source': 'database' if is_dict_input else 'dataframe',
                                   'period_used': original_period,
                                   'period_support': True,
                                   'api_version': '3.8_optimized'
                               })
        
        # 优先使用标准化单元
        standard_unit = self._get_standard_unit()
        if standard_unit:
            try:
                result = standard_unit.calculate_score(data, seasonal_factors)
                result.metadata.update({
                    'unit_type': 'standard',
                    'rows_processed': len(data),
                    'data_source': 'database' if is_dict_input else 'dataframe',
                    'period_used': original_period,
                    'period_support': True,
                    'api_version': '3.8_optimized'
                })
                return result
            except Exception as e:
                logger.warning(f"标准化MMT单元计算失败: {e}，回退到适配器")
        
        # 回退到适配器单元
        adapter_unit = self._get_adapter_unit()
        result = adapter_unit.calculate_score(data, seasonal_factors)
        result.metadata.update({
            'unit_type': 'adapter',
            'rows_processed': len(data),
            'data_source': 'database' if is_dict_input else 'dataframe',
            'period_used': original_period,
            'period_support': True,
            'api_version': '3.8_optimized'
        })
        return result
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        standard_unit = self._get_standard_unit()
        if standard_unit:
            return standard_unit.validate_data(data)
        else:
            return self._get_adapter_unit().validate_data(data)
    
    def get_required_columns(self) -> List[str]:
        standard_unit = self._get_standard_unit()
        if standard_unit:
            return standard_unit.get_required_columns()
        else:
            return self._get_adapter_unit().get_required_columns()
    
    def get_min_data_points(self) -> int:
        standard_unit = self._get_standard_unit()
        if standard_unit:
            return standard_unit.get_min_data_points()
        else:
            return self._get_adapter_unit().get_min_data_points()


class MCSIRSIScoringUnitWrapper(BaseScoringUnit):
    """MCSI RSI计分单元包装器 - 支持混合数据输入"""
    
    def __init__(self):
        super().__init__(
            unit_id='mcsi_rsi_wrapper',
            name='MCSI RSI包装器',
            description='支持混合数据输入的MCSI RSI包装器',
            min_score=-100.0,
            max_score=100.0
        )
        self._standard_unit = None
        self._adapter_unit = None
    
    def _get_standard_unit(self):
        """获取标准化单元"""
        if self._standard_unit is None:
            try:
                from .mcsi_rsi_scoring import MCSIRSIScoringUnit as StandardUnit
                self._standard_unit = StandardUnit()
            except ImportError:
                pass
        return self._standard_unit
    
    def _get_adapter_unit(self):
        """获取适配器单元"""
        if self._adapter_unit is None:
            self._adapter_unit = MCSIRSIScoringUnitAdapter()
        return self._adapter_unit
    
    def calculate_score(self, data: Union[pd.DataFrame, Dict[str, Any]], 
                      seasonal_factors: Optional[Dict] = None, period: str = 'daily') -> ScoringResult:
        """计算分数，支持混合数据输入"""
        # 记住原始数据类型用于metadata
        is_dict_input = isinstance(data, dict)
        
        # 提取期间参数
        original_period = data.get('period', period) if is_dict_input else period
        
        # 如果传入的是字典格式，使用DataProvider处理
        if is_dict_input:
            data, seasonal_factors = MCSIAdapter.prepare_data(data, period=original_period)
        
        if data.empty:
            return ScoringResult(score=0.0, signal='neutral', confidence=0.0,
                               description='无有效数据', 
                               metadata={
                                   'error': 'empty_data',
                                   'rows_processed': 0,
                                   'data_source': 'database' if is_dict_input else 'dataframe',
                                   'period_used': original_period,
                                   'period_support': True,
                                   'api_version': '3.8_optimized'
                               })
        
        # 优先使用标准化单元
        standard_unit = self._get_standard_unit()
        if standard_unit:
            try:
                result = standard_unit.calculate_score(data, seasonal_factors)
                result.metadata.update({
                    'unit_type': 'standard',
                    'rows_processed': len(data),
                    'data_source': 'database' if is_dict_input else 'dataframe',
                    'period_used': original_period,
                    'period_support': True,
                    'api_version': '3.8_optimized'
                })
                return result
            except Exception as e:
                logger.warning(f"标准化RSI单元计算失败: {e}，回退到适配器")
        
        # 回退到适配器单元
        adapter_unit = self._get_adapter_unit()
        result = adapter_unit.calculate_score(data, seasonal_factors)
        result.metadata.update({
            'unit_type': 'adapter',
            'rows_processed': len(data),
            'data_source': 'database' if is_dict_input else 'dataframe',
            'period_used': original_period,
            'period_support': True,
            'api_version': '3.8_optimized'
        })
        return result
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        standard_unit = self._get_standard_unit()
        if standard_unit:
            return standard_unit.validate_data(data)
        else:
            return self._get_adapter_unit().validate_data(data)
    
    def get_required_columns(self) -> List[str]:
        standard_unit = self._get_standard_unit()
        if standard_unit:
            return standard_unit.get_required_columns()
        else:
            return self._get_adapter_unit().get_required_columns()
    
    def get_min_data_points(self) -> int:
        standard_unit = self._get_standard_unit()
        if standard_unit:
            return standard_unit.get_min_data_points()
        else:
            return self._get_adapter_unit().get_min_data_points()


class MCSITTMScoringUnitWrapper(BaseScoringUnit):
    """MCSI TTM计分单元包装器 - 支持混合数据输入"""
    
    def __init__(self):
        super().__init__(
            unit_id='mcsi_ttm_wrapper',
            name='MCSI TTM包装器',
            description='支持混合数据输入的MCSI TTM包装器',
            min_score=-100.0,
            max_score=100.0
        )
        self._standard_unit = None
        self._adapter_unit = None
    
    def _get_standard_unit(self):
        """获取标准化单元"""
        if self._standard_unit is None:
            try:
                from .mcsi_ttm_scoring import MCSITTMScoringUnit as StandardUnit
                self._standard_unit = StandardUnit()
            except ImportError:
                pass
        return self._standard_unit
    
    def _get_adapter_unit(self):
        """获取适配器单元"""
        if self._adapter_unit is None:
            self._adapter_unit = MCSITTMScoringUnitAdapter()
        return self._adapter_unit
    
    def calculate_score(self, data: Union[pd.DataFrame, Dict[str, Any]], 
                      seasonal_factors: Optional[Dict] = None, period: str = 'daily') -> ScoringResult:
        """计算分数，支持混合数据输入"""
        # 记住原始数据类型用于metadata
        is_dict_input = isinstance(data, dict)
        
        # 提取期间参数
        original_period = data.get('period', period) if is_dict_input else period
        
        # 如果传入的是字典格式，使用DataProvider处理
        if is_dict_input:
            data, seasonal_factors = MCSIAdapter.prepare_data(data, period=original_period)
        
        if data.empty:
            return ScoringResult(score=0.0, signal='neutral', confidence=0.0,
                               description='无有效数据', 
                               metadata={
                                   'error': 'empty_data',
                                   'rows_processed': 0,
                                   'data_source': 'database' if is_dict_input else 'dataframe',
                                   'period_used': original_period,
                                   'period_support': True,
                                   'api_version': '3.8_optimized'
                               })
        
        # 优先使用标准化单元
        standard_unit = self._get_standard_unit()
        if standard_unit:
            try:
                result = standard_unit.calculate_score(data, seasonal_factors)
                result.metadata.update({
                    'unit_type': 'standard',
                    'rows_processed': len(data),
                    'data_source': 'database' if is_dict_input else 'dataframe',
                    'period_used': original_period,
                    'period_support': True,
                    'api_version': '3.8_optimized'
                })
                return result
            except Exception as e:
                logger.warning(f"标准化TTM单元计算失败: {e}，回退到适配器")
        
        # 回退到适配器单元
        adapter_unit = self._get_adapter_unit()
        result = adapter_unit.calculate_score(data, seasonal_factors)
        result.metadata.update({
            'unit_type': 'adapter',
            'rows_processed': len(data),
            'data_source': 'database' if is_dict_input else 'dataframe',
            'period_used': original_period,
            'period_support': True,
            'api_version': '3.8_optimized'
        })
        return result
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        standard_unit = self._get_standard_unit()
        if standard_unit:
            return standard_unit.validate_data(data)
        else:
            return self._get_adapter_unit().validate_data(data)
    
    def get_required_columns(self) -> List[str]:
        standard_unit = self._get_standard_unit()
        if standard_unit:
            return standard_unit.get_required_columns()
        else:
            return self._get_adapter_unit().get_required_columns()
    
    def get_min_data_points(self) -> int:
        standard_unit = self._get_standard_unit()
        if standard_unit:
            return standard_unit.get_min_data_points()
        else:
            return self._get_adapter_unit().get_min_data_points()


class MCSIMACDScoringUnitAdapter(BaseScoringUnit):
    """MCSI MACD计分单元适配器"""
    
    def __init__(self):
        super().__init__(
            unit_id='mcsi_macd_unit',
            name='MCSI MACD计分单元',
            description='基于MCSI MACD指标的增强版计分单元（适配版）',
            min_score=-10.0,
            max_score=10.0
        )
        self._indicator = None
    
    def _get_indicator(self):
        if self._indicator is None:
            try:
                from mcsi_macd import MCSIMACDIndicator
                self._indicator = MCSIMACDIndicator()
            except ImportError:
                self._indicator = None
        return self._indicator
    
    def calculate_score(self, data: Union[pd.DataFrame, Dict[str, Any]], 
                      seasonal_factors: Optional[Dict] = None) -> ScoringResult:
        """计算分数，支持混合数据输入"""
        # 如果传入的是字典格式，尝试使用DataProvider获取数据
        if isinstance(data, dict):
            data, seasonal_factors = MCSIAdapter.prepare_data(data)
        
        if data.empty:
            return ScoringResult(score=0.0, signal='neutral', confidence=0.0,
                               description='无有效数据', metadata={'error': 'empty_data'})
        
        indicator = self._get_indicator()
        if indicator is None:
            return ScoringResult(score=0.0, signal='neutral', confidence=0.0, 
                               description='MCSI MACD指标导入失败', metadata={'error': 'import_failed'})
        
        try:
            # 正确的接口调用
            result = indicator.calculate(data['close'].values.astype(np.float64))
            latest_score = result.get('macd_score', [0.0])[-1] if len(result.get('macd_score', [])) > 0 else 0.0
            
            if np.isnan(latest_score) or np.isinf(latest_score):
                latest_score = 0.0
            
            normalized_score = np.clip(latest_score / 10.0, -10.0, 10.0)
            
            # 添加季节性因子到元数据
            metadata = {'original_score': float(latest_score), 'adapter_version': True}
            if seasonal_factors:
                metadata['seasonal_factors'] = seasonal_factors
            
            return ScoringResult(
                score=float(normalized_score),
                raw_value=float(latest_score),
                signal='bullish' if latest_score > 0 else 'bearish' if latest_score < 0 else 'neutral',
                confidence=min(1.0, abs(latest_score) / 100.0),
                description=f'MCSI MACD分析({normalized_score:.2f})',
                metadata=metadata
            )
        except Exception as e:
            return ScoringResult(score=0.0, signal='neutral', confidence=0.0,
                               description=f'MCSI MACD计算错误: {str(e)}', metadata={'error': str(e)})
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        return 'close' in data.columns and len(data) >= 50
    
    def get_required_columns(self) -> List[str]:
        return ['close']
    
    def get_min_data_points(self) -> int:
        return 50


class MCSIMMTScoringUnitAdapter(BaseScoringUnit):
    """MCSI MMT计分单元适配器"""
    
    def __init__(self):
        super().__init__(
            unit_id='mcsi_mmt_unit',
            name='MCSI MMT计分单元',
            description='基于MCSI MMT指标的多重复合动量计分单元（适配版）',
            min_score=-10.0,
            max_score=10.0
        )
        self._indicator = None
    
    def _get_indicator(self):
        if self._indicator is None:
            try:
                from mcsi_mmt import MCSIMMTIndicator
                self._indicator = MCSIMMTIndicator()
            except ImportError:
                self._indicator = None
        return self._indicator
    
    def calculate_score(self, data: Union[pd.DataFrame, Dict[str, Any]], 
                      seasonal_factors: Optional[Dict] = None) -> ScoringResult:
        """计算分数，支持混合数据输入"""
        # 如果传入的是字典格式，尝试使用DataProvider获取数据
        if isinstance(data, dict):
            data, seasonal_factors = MCSIAdapter.prepare_data(data)
        
        if data.empty:
            return ScoringResult(score=0.0, signal='neutral', confidence=0.0,
                               description='无有效数据', metadata={'error': 'empty_data'})
        
        indicator = self._get_indicator()
        if indicator is None:
            return ScoringResult(score=0.0, signal='neutral', confidence=0.0,
                               description='MCSI MMT指标导入失败', metadata={'error': 'import_failed'})
        
        try:
            # 正确的三参数接口调用
            result = indicator.calculate(
                data['close'].values.astype(np.float64),
                data['high'].values.astype(np.float64),
                data['low'].values.astype(np.float64)
            )
            latest_score = result.get('mmt_score', [0.0])[-1] if len(result.get('mmt_score', [])) > 0 else 0.0
            
            if np.isnan(latest_score) or np.isinf(latest_score):
                latest_score = 0.0
            
            normalized_score = np.clip(latest_score / 10.0, -10.0, 10.0)
            
            # 添加季节性因子到元数据
            metadata = {'original_score': float(latest_score), 'adapter_version': True}
            if seasonal_factors:
                metadata['seasonal_factors'] = seasonal_factors
            
            return ScoringResult(
                score=float(normalized_score),
                raw_value=float(latest_score),
                signal='bullish' if latest_score > 0 else 'bearish' if latest_score < 0 else 'neutral',
                confidence=min(1.0, abs(latest_score) / 100.0),
                description=f'MCSI MMT分析({normalized_score:.2f})',
                metadata=metadata
            )
        except Exception as e:
            return ScoringResult(score=0.0, signal='neutral', confidence=0.0,
                               description=f'MCSI MMT计算错误: {str(e)}', metadata={'error': str(e)})
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        required_cols = ['close', 'high', 'low']
        return all(col in data.columns for col in required_cols) and len(data) >= 70
    
    def get_required_columns(self) -> List[str]:
        return ['close', 'high', 'low']
    
    def get_min_data_points(self) -> int:
        return 70


class MCSIRSIScoringUnitAdapter(BaseScoringUnit):
    """MCSI RSI计分单元适配器"""
    
    def __init__(self):
        super().__init__(
            unit_id='mcsi_rsi_unit',
            name='MCSI RSI计分单元',
            description='基于MCSI RSI指标的增强版计分单元（适配版）',
            min_score=-10.0,
            max_score=10.0
        )
        self._indicator = None
    
    def _get_indicator(self):
        if self._indicator is None:
            try:
                from mcsi_rsi import MCSIRSIIndicator
                self._indicator = MCSIRSIIndicator()
            except ImportError:
                self._indicator = None
        return self._indicator
    
    def calculate_score(self, data: pd.DataFrame) -> ScoringResult:
        indicator = self._get_indicator()
        if indicator is None:
            return ScoringResult(score=0.0, signal='neutral', confidence=0.0,
                               description='MCSI RSI指标导入失败', metadata={'error': 'import_failed'})
        
        try:
            # DataFrame接口调用
            result = indicator.calculate(data)
            latest_score = result.get('rsi_score', [50.0])[-1] if len(result.get('rsi_score', [])) > 0 else 50.0
            
            if np.isnan(latest_score) or np.isinf(latest_score):
                latest_score = 50.0
            
            normalized_score = (latest_score - 50.0) / 5.0
            normalized_score = np.clip(normalized_score, -10.0, 10.0)
            
            return ScoringResult(
                score=float(normalized_score),
                raw_value=float(latest_score),
                signal='bullish' if latest_score > 55 else 'bearish' if latest_score < 45 else 'neutral',
                confidence=min(1.0, abs(latest_score - 50.0) / 50.0),
                description=f'MCSI RSI分析({normalized_score:.2f})',
                metadata={'original_score': float(latest_score), 'adapter_version': True}
            )
        except Exception as e:
            return ScoringResult(score=0.0, signal='neutral', confidence=0.0,
                               description=f'MCSI RSI计算错误: {str(e)}', metadata={'error': str(e)})
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        required_cols = ['close', 'high', 'low', 'open', 'volume']
        return all(col in data.columns for col in required_cols) and len(data) >= 50
    
    def get_required_columns(self) -> List[str]:
        return ['close', 'high', 'low', 'open', 'volume']
    
    def get_min_data_points(self) -> int:
        return 50


class MCSITTMScoringUnitAdapter(BaseScoringUnit):
    """MCSI TTM计分单元适配器（原TD9指标）"""
    
    def __init__(self):
        super().__init__(
            unit_id='mcsi_ttm_unit',
            name='MCSI TTM计分单元',
            description='基于MCSI TTM指标的时序计分单元（适配版）',
            min_score=-10.0,
            max_score=10.0
        )
        self._indicator = None
    
    def _get_indicator(self):
        if self._indicator is None:
            try:
                # 尝试导入新的TTM指标
                from mcsi_ttm import MCSITTMIndicator
                self._indicator = MCSITTMIndicator()
            except ImportError:
                try:
                    # 向后兼容，尝试导入旧的TD9指标
                    from mcsi_td9 import MCSITD9Indicator
                    self._indicator = MCSITD9Indicator()
                except ImportError:
                    self._indicator = None
        return self._indicator
    
    def calculate_score(self, data: pd.DataFrame) -> ScoringResult:
        indicator = self._get_indicator()
        if indicator is None:
            return ScoringResult(score=0.0, signal='neutral', confidence=0.0,
                               description='MCSI TTM指标导入失败', metadata={'error': 'import_failed'})
        
        try:
            # numpy数组接口调用
            result = indicator.calculate(data['close'].values.astype(np.float64))
            # 兼容两种结果键名
            latest_score = (result.get('ttm_score', result.get('td9_score', [0.0])))[-1] if len(result.get('ttm_score', result.get('td9_score', []))) > 0 else 0.0
            
            if np.isnan(latest_score) or np.isinf(latest_score):
                latest_score = 0.0
            
            normalized_score = np.clip(latest_score / 10.0, -10.0, 10.0)
            
            return ScoringResult(
                score=float(normalized_score),
                raw_value=float(latest_score),
                signal='bullish' if latest_score > 0 else 'bearish' if latest_score < 0 else 'neutral',
                confidence=min(1.0, abs(latest_score) / 100.0),
                description=f'MCSI TTM分析({normalized_score:.2f})',
                metadata={'original_score': float(latest_score), 'adapter_version': True}
            )
        except Exception as e:
            return ScoringResult(score=0.0, signal='neutral', confidence=0.0,
                               description=f'MCSI TTM计算错误: {str(e)}', metadata={'error': str(e)})
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        return 'close' in data.columns and len(data) >= 25
    
    def get_required_columns(self) -> List[str]:
        return ['close']
    
    def get_min_data_points(self) -> int:
        return 25


# 导出函数，供Web API使用
def MCSIMACDScoringUnit():
    """创建MCSI MACD计分单元"""
    return MCSIAdapter.create_mcsi_macd_unit()

def MCSIMMTScoringUnit():
    """创建MCSI MMT计分单元"""
    return MCSIAdapter.create_mcsi_mmt_unit()

def MCSIRSIScoringUnit():
    """创建MCSI RSI计分单元"""
    return MCSIAdapter.create_mcsi_rsi_unit()

def MCSITTMScoringUnit():
    """创建MCSI TTM计分单元"""
    return MCSIAdapter.create_mcsi_ttm_unit()

# 向后兼容性
def MCSITD9ScoringUnit():
    """创建MCSI TD9计分单元（已弃用，使用TTM替代）"""
    logger.warning("MCSITD9ScoringUnit已弃用，请使用MCSITTMScoringUnit")
    return MCSIAdapter.create_mcsi_ttm_unit()
