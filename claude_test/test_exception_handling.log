[2025-08-20 08:40:29,175] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,176] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
[2025-08-20 08:40:29,176] WARNING - core.scoring_units.base_scoring_unit.mcsi_macd - 数据长度不足: 需要49行，实际5行
[2025-08-20 08:40:29,177] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,177] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
[2025-08-20 08:40:29,180] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,180] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
[2025-08-20 08:40:29,180] WARNING - core.scoring_units.base_scoring_unit.mcsi_macd - 收盘价包含非正值
[2025-08-20 08:40:29,180] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 08:40:29,180] INFO - core.scoring_units.base_scoring_unit.mcsi_mmt - ✅ 成功加载MMT源代码实现
[2025-08-20 08:40:29,181] WARNING - core.scoring_units.base_scoring_unit.mcsi_mmt - close包含非正值
[2025-08-20 08:40:29,181] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 08:40:29,181] INFO - core.scoring_units.base_scoring_unit.mcsi_rsi - ✅ 成功加载RSI源代码实现
[2025-08-20 08:40:29,181] WARNING - core.scoring_units.base_scoring_unit.mcsi_rsi - 收盘价包含非正值
[2025-08-20 08:40:29,181] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 08:40:29,181] INFO - core.scoring_units.base_scoring_unit.mcsi_ttm - ✅ 成功加载TTM源代码实现
[2025-08-20 08:40:29,181] WARNING - core.scoring_units.base_scoring_unit.mcsi_ttm - 收盘价包含非正值
[2025-08-20 08:40:29,181] INFO - core.scoring_units.mcsi_macd_scoring - 测试MCSI MACD日志信息
[2025-08-20 08:40:29,181] WARNING - core.scoring_units.mcsi_macd_scoring - 测试MCSI MACD警告信息
[2025-08-20 08:40:29,181] INFO - core.scoring_units.mcsi_adapter - 测试适配器日志信息
[2025-08-20 08:40:29,181] ERROR - core.scoring_units.mcsi_macd_scoring - 捕获测试异常: 测试异常日志
[2025-08-20 08:40:29,181] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 08:40:29,182] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
[2025-08-20 08:40:29,183] INFO - core.scoring_units.base_scoring_unit.mcsi_mmt - ✅ 成功加载MMT源代码实现
[2025-08-20 08:40:29,189] INFO - core.scoring_units.base_scoring_unit.mcsi_rsi - ✅ 成功加载RSI源代码实现
[2025-08-20 08:40:29,190] INFO - core.scoring_units.base_scoring_unit.mcsi_ttm - ✅ 成功加载TTM源代码实现
[2025-08-20 08:40:29,191] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,191] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
[2025-08-20 08:40:29,191] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,196] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
[2025-08-20 08:40:29,196] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,196] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,201] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 08:40:29,201] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
[2025-08-20 08:40:29,201] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
[2025-08-20 08:40:29,201] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
