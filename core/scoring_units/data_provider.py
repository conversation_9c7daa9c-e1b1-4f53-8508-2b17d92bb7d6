#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DataProvider类
负责混合数据输入，优先从数据库获取，失败时回落到OHLC数据
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, Any, Union, Tu<PERSON>
from datetime import datetime, timedelta
import logging
from functools import lru_cache
import hashlib
import json

# SQLAlchemy imports
try:
    from sqlalchemy import create_engine, text, MetaData, Table, select
    from sqlalchemy.orm import sessionmaker
    from sqlalchemy.pool import QueuePool
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False
    logging.warning("SQLAlchemy not available, database features will be disabled")

logger = logging.getLogger(__name__)


class DataProvider:
    """
    数据提供者类
    提供统一的数据访问接口，支持数据库和OHLC数据源
    """
    
    # 缓存配置
    CACHE_SIZE = 128  # LRU缓存大小
    CACHE_TTL = 3600  # 缓存过期时间（秒）
    
    # 数据库配置
    DB_POOL_SIZE = 5
    DB_MAX_OVERFLOW = 10
    DB_POOL_TIMEOUT = 30
    DB_POOL_RECYCLE = 3600
    
    def __init__(self, cache_enabled: bool = True):
        """
        初始化DataProvider
        
        Args:
            cache_enabled: 是否启用缓存
        """
        self.cache_enabled = cache_enabled
        self._cache = {}
        self._cache_timestamps = {}
        self._db_engines = {}  # 存储不同数据库的引擎
        
    def get_stock_data(self, 
                      db_conn: Optional[Union[str, Any]] = None,
                      symbol: Optional[str] = None,
                      start_date: Optional[Union[str, datetime]] = None,
                      end_date: Optional[Union[str, datetime]] = None,
                      ohlc: Optional[pd.DataFrame] = None,
                      period: str = 'daily',
                      include_seasonal: bool = False) -> pd.DataFrame:
        """
        获取股票数据的主接口
        
        Args:
            db_conn: 数据库连接（连接字符串或SQLAlchemy引擎）
            symbol: 股票代码（如'000001'或'cnindex_000001_上证指数'）
            start_date: 开始日期
            end_date: 结束日期
            ohlc: 备用的OHLC DataFrame数据
            period: 时间周期 ('daily', 'weekly', 'monthly')
            include_seasonal: 是否包含季节性因子
            
        Returns:
            DataFrame: 数据框架
            
        优先级：
            1. 如果提供了db_conn和symbol，尝试从数据库获取指定period的数据
            2. 如果数据库获取失败，回落到ohlc（仅对daily有效）
            3. 如果都失败，返回空DataFrame
        """
        logger.info(f"获取{period}数据: symbol={symbol}, start={start_date}, end={end_date}")
        
        # 生成缓存键（包含period）
        cache_key = None
        if self.cache_enabled and symbol:
            cache_key = self._generate_cache_key(symbol, start_date, end_date, period)
            cached_data = self._get_cached_data(cache_key)
            if cached_data is not None:
                logger.info(f"从缓存返回{period}数据: {symbol}")
                return cached_data
        
        df = pd.DataFrame()
        
        # 尝试从数据库获取指定period的数据
        if db_conn and symbol and SQLALCHEMY_AVAILABLE:
            try:
                df = self._fetch_from_database(db_conn, symbol, start_date, end_date, period)
                if not df.empty:
                    logger.info(f"从数据库获取{period}数据成功: {symbol}, 行数: {len(df)}")
                    # 缓存数据
                    if self.cache_enabled and cache_key:
                        self._cache_data(cache_key, df)
            except Exception as e:
                logger.warning(f"数据库{period}数据获取失败: {e}")
                # 继续尝试备用方案
        
        # 如果数据库获取失败或为空，且period为daily时，使用备用OHLC数据
        if df.empty and ohlc is not None and period == 'daily':
            logger.info(f"使用备用OHLC数据 (period={period})")
            df = self._process_ohlc_data(ohlc, start_date, end_date)
        elif df.empty and period != 'daily':
            logger.warning(f"无法获取{period}数据，且不支持OHLC回退")
        
        # 数据验证和清洗
        if not df.empty:
            df = self._validate_and_clean_data(df)
        
        return df
    
    def _fetch_from_database(self, 
                           db_conn: Union[str, Any],
                           symbol: str,
                           start_date: Optional[Union[str, datetime]],
                           end_date: Optional[Union[str, datetime]],
                           period: str = 'daily') -> pd.DataFrame:
        """
        从数据库获取指定period的数据
        
        支持两种表命名模式：
        1. 统一的stock_data表，通过symbol和period字段过滤
        2. 分表模式，如cnindex_000001_上证指数，通过period字段过滤
        
        Args:
            period: 时间周期 ('daily', 'weekly', 'monthly')
        """
        # 获取或创建数据库引擎
        engine = self._get_db_engine(db_conn)
        
        # 处理日期
        if start_date and isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if end_date and isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)
        
        # 尝试两种查询模式
        df = pd.DataFrame()
        
        # 模式1: 统一表查询（包含period）
        try:
            query = self._build_unified_table_query(symbol, start_date, end_date, period)
            df = pd.read_sql(query, engine)
        except Exception as e:
            logger.debug(f"统一表{period}查询失败: {e}")
            
        # 模式2: 分表查询（包含period）
        if df.empty:
            try:
                query = self._build_split_table_query(symbol, start_date, end_date, period)
                df = pd.read_sql(query, engine)
            except Exception as e:
                logger.debug(f"分表{period}查询失败: {e}")
        
        # 标准化列名
        if not df.empty:
            df = self._standardize_columns(df)
        
        return df
    
    def _build_unified_table_query(self, symbol: str, start_date: Optional[datetime], 
                                  end_date: Optional[datetime], period: str = 'daily') -> str:
        """构建统一表查询SQL（参数化查询，包含period）"""
        base_query = """
        SELECT timestamp as date, open, high, low, close, volume
        FROM stock_data
        WHERE symbol = :symbol AND period = :period
        """
        
        conditions = []
        if start_date:
            conditions.append(f"AND timestamp >= :start_date")
        if end_date:
            conditions.append(f"AND timestamp <= :end_date")
        
        query = base_query + " ".join(conditions) + " ORDER BY timestamp"
        
        # 使用SQLAlchemy的text()进行参数化
        from sqlalchemy import text
        return text(query).bindparams(
            symbol=symbol,
            period=period,
            start_date=start_date if start_date else '1900-01-01',
            end_date=end_date if end_date else '2100-12-31'
        )
    
    def _build_split_table_query(self, symbol: str, start_date: Optional[datetime],
                                end_date: Optional[datetime], period: str = 'daily') -> str:
        """构建分表查询SQL（包含period）"""
        # 清理表名（防止SQL注入）
        table_name = self._sanitize_table_name(symbol)
        
        base_query = f"""
        SELECT timestamp as date, open, high, low, close, volume
        FROM "{table_name}"
        WHERE period = '{period}'
        """
        
        conditions = []
        if start_date:
            conditions.append(f"AND timestamp >= '{start_date}'")
        if end_date:
            conditions.append(f"AND timestamp <= '{end_date}'")
        
        return base_query + " ".join(conditions) + " ORDER BY timestamp"
    
    def _sanitize_table_name(self, symbol: str) -> str:
        """清理表名，防止SQL注入"""
        # 只允许字母、数字、下划线和中文
        import re
        clean_symbol = re.sub(r'[^\w\u4e00-\u9fa5_]', '_', symbol)
        return clean_symbol
    
    def _get_db_engine(self, db_conn: Union[str, Any]):
        """获取或创建数据库引擎"""
        if isinstance(db_conn, str):
            # 连接字符串，创建新引擎
            if db_conn not in self._db_engines:
                self._db_engines[db_conn] = create_engine(
                    db_conn,
                    pool_size=self.DB_POOL_SIZE,
                    max_overflow=self.DB_MAX_OVERFLOW,
                    pool_timeout=self.DB_POOL_TIMEOUT,
                    pool_recycle=self.DB_POOL_RECYCLE,
                    pool_pre_ping=True  # 检查连接健康
                )
            return self._db_engines[db_conn]
        else:
            # 已有的引擎对象
            return db_conn
    
    def _process_ohlc_data(self, ohlc: pd.DataFrame, 
                         start_date: Optional[Union[str, datetime]],
                         end_date: Optional[Union[str, datetime]]) -> pd.DataFrame:
        """处理OHLC数据"""
        df = ohlc.copy()
        
        # 确保有日期列
        if 'date' not in df.columns and 'Date' not in df.columns:
            if 'timestamp' in df.columns:
                df['date'] = pd.to_datetime(df['timestamp'])
            elif df.index.name in ['date', 'Date', 'timestamp']:
                df['date'] = df.index
            else:
                # 假设索引是日期
                df['date'] = pd.to_datetime(df.index)
        
        # 标准化列名
        df = self._standardize_columns(df)
        
        # 过滤日期范围
        if start_date or end_date:
            if start_date:
                start_date = pd.to_datetime(start_date) if isinstance(start_date, str) else start_date
                df = df[df['date'] >= start_date]
            if end_date:
                end_date = pd.to_datetime(end_date) if isinstance(end_date, str) else end_date
                df = df[df['date'] <= end_date]
        
        return df
    
    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化列名"""
        column_mapping = {
            'Date': 'date',
            'timestamp': 'date',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume',
            'vol': 'volume'
        }
        
        df = df.rename(columns=column_mapping)
        
        # 确保有Date列用于兼容性
        if 'date' in df.columns and 'Date' not in df.columns:
            df['Date'] = df['date']
        
        # 确保日期列是datetime类型
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
        
        # 确保数值列是float类型
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        return df
    
    def _validate_and_clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证和清洗数据"""
        if df.empty:
            return df
        
        original_len = len(df)
        
        # 移除重复日期
        if 'date' in df.columns:
            df = df.drop_duplicates(subset=['date'])
            df = df.sort_values('date')
        
        # 移除无效价格
        price_cols = ['open', 'high', 'low', 'close']
        for col in price_cols:
            if col in df.columns:
                df = df[df[col] > 0]
        
        # 验证价格逻辑
        if all(col in df.columns for col in ['high', 'low', 'open', 'close']):
            df = df[(df['high'] >= df['low']) & 
                   (df['high'] >= df['open']) & 
                   (df['high'] >= df['close']) &
                   (df['low'] <= df['open']) &
                   (df['low'] <= df['close'])]
        
        # 处理缺失值
        df = df.dropna(subset=price_cols, how='any')
        
        # 重置索引
        df = df.reset_index(drop=True)
        
        cleaned_rows = original_len - len(df)
        if cleaned_rows > 0:
            logger.info(f"数据清洗: 移除了 {cleaned_rows} 行无效数据")
        
        return df
    
    
    def _generate_cache_key(self, symbol: str, start_date: Any, end_date: Any, period: str = 'daily') -> str:
        """生成缓存键（包含period）"""
        key_str = f"{symbol}_{period}_{start_date}_{end_date}"
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def _get_cached_data(self, cache_key: str) -> Optional[pd.DataFrame]:
        """获取缓存数据"""
        if cache_key in self._cache:
            timestamp = self._cache_timestamps.get(cache_key, 0)
            if datetime.now().timestamp() - timestamp < self.CACHE_TTL:
                logger.debug(f"缓存命中: {cache_key}")
                return self._cache[cache_key].copy()
            else:
                # 缓存过期
                del self._cache[cache_key]
                del self._cache_timestamps[cache_key]
        return None
    
    def _cache_data(self, cache_key: str, data: pd.DataFrame):
        """缓存数据"""
        # 简单的LRU实现
        if len(self._cache) >= self.CACHE_SIZE:
            # 删除最旧的缓存项
            oldest_key = min(self._cache_timestamps, key=self._cache_timestamps.get)
            del self._cache[oldest_key]
            del self._cache_timestamps[oldest_key]
        
        self._cache[cache_key] = data.copy()
        self._cache_timestamps[cache_key] = datetime.now().timestamp()
        logger.debug(f"数据已缓存: {cache_key}")
    
    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        self._cache_timestamps.clear()
        logger.info("缓存已清空")
    
    def get_connection_string(self, host: str = '***********', port: int = 5433,
                            database: str = 'fintech_db', user: str = 'postgres',
                            password: str = 'robot2025') -> str:
        """
        生成PostgreSQL连接字符串
        
        Args:
            host: 数据库主机
            port: 端口
            database: 数据库名
            user: 用户名
            password: 密码
            
        Returns:
            SQLAlchemy连接字符串
        """
        return f"postgresql://{user}:{password}@{host}:{port}/{database}"


# 索引优化建议（作为注释文档）
"""
数据库索引优化建议：

1. 主要索引：
   CREATE INDEX idx_stock_data_symbol_timestamp ON stock_data(symbol, timestamp);
   CREATE INDEX idx_stock_data_timestamp ON stock_data(timestamp);
   
2. 分表索引（对每个股票表）：
   CREATE INDEX idx_{table_name}_timestamp ON {table_name}(timestamp);
   CREATE INDEX idx_{table_name}_date_range ON {table_name}(timestamp) 
       WHERE timestamp >= '2020-01-01';  -- 部分索引，只索引近期数据

3. 分区表建议（大数据量）：
   - 按年份或月份分区
   - 使用PostgreSQL的声明式分区
   
4. 查询优化：
   - 使用EXPLAIN ANALYZE分析慢查询
   - 考虑使用物化视图缓存常用聚合数据
   - 定期运行VACUUM和ANALYZE维护表统计信息

5. 连接池优化：
   - 根据并发量调整pool_size和max_overflow
   - 使用pgbouncer等连接池中间件

6. 缓存策略：
   - 应用层：LRU缓存热点数据
   - 数据库层：调整shared_buffers和effective_cache_size
   - Redis缓存：考虑引入Redis缓存频繁访问的数据
"""